<!--begin::Sidebar-->
<div id="kt_app_sidebar" class="app-sidebar flex-column bg-white border-end" data-kt-drawer="true" data-kt-drawer-name="app-sidebar"
    data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px"
    data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="app-sidebar-logo px-6 border-0" id="kt_app_sidebar_logo">
        <a href="{{url('home')}}">
            <img alt="Logo" src="{{asset('')}}{{ App\Models\Setting::first()->logo??'' }}"
                class="app-sidebar-logo-default" />
            <img alt="Logo" src="{{ asset('website') }}/assets/media/logos/default-small.svg"
                class="h-20px app-sidebar-logo-minimize" />
        </a>
    </div>

    <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper">
            <div id="kt_app_sidebar_menu_scroll" class=" my-5 mx-3" data-kt-scroll="true"
                data-kt-scroll-activate="true" data-kt-scroll-height="auto"
                data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px"
                data-kt-scroll-save-state="true">
                <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6" id="#kt_app_sidebar_menu"
                    data-kt-menu="true" data-kt-menu-expand="false">

                    <!-- dashboard menu items list start -->
                    @if(auth()->user()->hasRole('developer'))
                    <div class="menu-item pt-5">
                        <div class="menu-content">
                            <span class="menu-heading fw-bold text-uppercase fs-7">Developer</span>
                        </div>
                    </div>

                    <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                        <span class="menu-link">
                            <span class="menu-icon">
                                <i class="ki-duotone ki-abstract-28 fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </span>
                            <span class="menu-title">User Management</span>
                            <span class="menu-arrow"></span>
                        </span>
                        <div class="menu-sub menu-sub-accordion">
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                @can('crud-list')
                                <span class="menu-link">
                                    <span class="menu-bullet">
                                        <span class="bullet bullet-dot"></span>
                                    </span>
                                    <span class="menu-title">CRUD</span>
                                    <span class="menu-arrow"></span>
                                </span>
                                <div class="menu-sub menu-sub-accordion">
                                    <div class="menu-item">
                                        <a class="menu-link" href="{{ url('crud_generator') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">CRUD Generator</span>
                                        </a>
                                    </div>
                                </div>
                                @endcan
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    @can('user-list')
                                    <span class="menu-link">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Users</span>
                                        <span class="menu-arrow"></span>
                                    </span>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('users') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Users List</span>
                                            </a>
                                        </div>
                                    </div>
                                    @endcan
                                </div>

                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                    @can('role-list')
                                    <span class="menu-link">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Roles</span>
                                        <span class="menu-arrow"></span>
                                    </span>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('roles') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Roles List</span>
                                            </a>
                                        </div>
                                    </div>
                                    @endcan
                                </div>

                                <div class="menu-item">
                                    <a class="menu-link" href="javascript:void(0);">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Permissions</span>
                                    </a>
                                </div>

                                @can('settings-list')
                                <div class="menu-item">
                                    <a class="menu-link" href="{{ url('settings') }}">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Settings</span>
                                    </a>
                                </div>
                                @endcan
                            </div>
                        </div>
                    </div>
                    <hr>

                    @endif

                    <!-- admin menu items list start -->
                    @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('professional'))
                    <div class="menu-item">
                        <a class="menu-link {{ request()->is('home') ? 'active' : '' }}" href="{{ url('home') }}">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/images/icon-dashboard.svg">
                            </span>
                            <span class="menu-title">Dashboard</span>
                        </a>
                    </div>
                    @if(auth()->user()->hasRole('admin'))
                    <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                        <span class="menu-link">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/images/icon-professionals.svg">
                            </span>
                            <span class="menu-title">Professionals</span>
                        </span>
                        <div class="menu-sub menu-sub-accordion">
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/contractors') ? 'active' : '' }}" href="{{ route('admin.contractors') }}">
                                    <span class="menu-bullet"></span>
                                    <span class="menu-title">Contractors</span>
                                </a>
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/title-escrow') ? 'active' : '' }}" href="{{ route('admin.title-escrow') }}">
                                    <span class="menu-bullet">
                                    </span>
                                    <span class="menu-title">Title & Escrow</span>
                                </a>
                            </div>
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/mortgage-lenders') ? 'active' : '' }}" href="{{ route('admin.mortgage-lenders') }}">
                                    <span class="menu-bullet">
                                    </span>
                                    <span class="menu-title">Mortgage Lenders</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <a class="menu-link {{ request()->is('admin/service-categories') ? 'active' : '' }}" href="{{ route('admin.service-categories') }}">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/images/icon-services.svg">
                            </span>
                            <span class="menu-title">Service Categories</span>
                        </a>
                    </div>
                    @endif

                    @if(auth()->user()->hasRole('professional'))
                    <div class="menu-item">
                        <a class="menu-link {{ request()->is('professional/projects') ? 'active' : '' }}" href="{{ route('professional.projects') }}">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/media/images/projects-icon.png">
                            </span>
                            <span class="menu-title">My Projects</span>
                        </a>
                    </div>

                    <div class="menu-item">
                        <a class="menu-link {{ request()->is('professional/quote-request') ? 'active' : '' }}" href="{{ route('professional.quote-request') }}">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/media/images/projects-icon.png">
                            </span>
                            <span class="menu-title">Quote Request</span>
                        </a>
                    </div>
                    @endif
                    <div class="menu-item">
                        <a class="menu-link {{ request()->is('admin/subscription') ? 'active' : '' }}" href="{{ route('admin.subscription') }}">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/images/icon-subscription.svg">
                            </span>
                            <span class="menu-title">Subscription</span>
                        </a>
                    </div>

                    <div class="menu-item">
                        <a class="menu-link {{ request()->is('admin/chats') ? 'active' : '' }}" href="javascript:void(0);">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/images/icon-chats.svg">
                            </span>
                            <span class="menu-title">Chats</span>
                        </a>
                    </div>

                    @if(auth()->user()->hasRole('admin'))
                    <div class="menu-item">
                        <a class="menu-link {{ request()->is('admin/cms') ? 'active' : '' }}" href="javascript:void(0);">
                            <span class="menu-icon">
                                <img src="{{asset('website')}}/assets/images/icon-cms.svg">
                            </span>
                            <span class="menu-title">CMS</span>
                        </a>
                    </div>
                    @endif

                    @endif

                    @foreach ($crud as $item)
                    @can($item->url . '-list')
                    <div class="menu-item">
                        <a class="menu-link {{ request()->is($item->url) ? 'active' : '' }}"
                            href="{{ url($item->url ?? 'home') }}">
                            <span class="menu-icon">
                                <i class="ki-duotone ki-abstract-28 fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </span>
                            <span class="menu-title">{{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->name) }}</span>
                        </a>
                    </div>
                    @endcan
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <div class="app-sidebar-footer flex-column-auto pt-2 pb-6 px-3" id="kt_app_sidebar_footer">
        @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('professional'))
        <div class="menu-item">
            <a class="menu-link {{ request()->is('notifications') ? 'active' : '' }}" href="{{ url('notifications') }}">
                <span class="menu-icon">
                    <img src="{{asset('website')}}/assets/images/icon-notification.svg">
                </span>
                <span class="menu-title">Notification</span>
            </a>
        </div>
        <div class="menu-item">
            <a class="menu-link {{ request()->is('profile_settings') ? 'active' : '' }}" href="{{ url('profile_settings') }}">
                <span class="menu-icon">
                    <img src="{{asset('website')}}/assets/images/icon-settings.svg">
                </span>
                <span class="menu-title">Settings</span>
            </a>
        </div>

        <hr>

        <div class="profile_info">
            <div class="d-flex align-items-center">
                <div class="symbol symbol-50px me-3">
                    <img src="{{asset('website')}}/assets/media/avatars/300-3.jpg" class="rounded-circle" alt="">
                </div>
                <div class="d-flex justify-content-start flex-column me-3">
                    <a href="javascript:void(0);" class="text-gray-800 fw-bold text-hover-primary mb-1 fs-6 text">Olivia Rhye</a>
                    <span class="text-gray-400 fw-semibold d-block fs-7 text"><EMAIL></span>
                </div>
                <a class="" href="{{ url('logout') }}">
                    <span class="menu-icon">
                        <img src="{{asset('website')}}/assets/images/icon-logout.svg">
                    </span>
                </a>
            </div>
        </div>
        @endif
        @if(auth()->user()->hasRole('developer'))

        <a href="{{url('html/demo1/dist')}}"
            class="btn btn-flex flex-center btn-custom btn-primary overflow-hidden text-nowrap px-0 h-40px w-100"
            data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss-="click"
            title="200+ in-house components and 3rd-party plugins">
            <span class="btn-label">Docs & Components</span>
            <i class="ki-duotone ki-document btn-icon fs-2 m-0">
                <span class="path1"></span>
                <span class="path2"></span>
            </i>
        </a>
        @endif
    </div>
</div>
<!--end::Sidebar-->