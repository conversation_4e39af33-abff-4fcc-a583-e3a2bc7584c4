<?php $__env->startSection('title', 'Register - Vadu'); ?>
<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 p-12 p-lg-10 first-stepper-form d-flex align-items-center justify-content-between flex-column">
            <form class="register-form form" method="POST" action="<?php echo e(route('signup')); ?>" id="registerationForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>

                <!-- Step 1 - Select Role -->
                <fieldset class="step-1 stepOne">
                    <div class="row justify-content-center">
                        <div class="col-md-8 col-lg-6 text-center">
                            <a href="<?php echo e(route('home')); ?>" class="d-block mb-5">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/logo-white.svg" alt="icon">
                            </a>
                            <h3 class="mb-10 text-white fs-24">Sign Up for Vadu</h3>
                            <div class="row gray-card-main justify-content-center g-3">
                                <div class="col-md-6">
                                    <label class="w-100">
                                        <input type="radio" name="role" value="customer" required class="d-none">
                                        <div class="gray-card action-button role-selector h-100">
                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/login-customer-icon.svg" alt="">
                                            <div>
                                                <h5 class="fs-24 fw-600 text-white">For Customer</h5>
                                            </div>
                                            <div class="">
                                                <p class="fs-14 light-white m-0">Get your Work Done by Professionals</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                <div class="col-md-6">
                                    <label class="w-100">
                                        <input type="radio" name="role" value="professional" required class="d-none">
                                        <div class="gray-card action-button role-selector h-100">
                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/login-professional-icon.svg" alt="">
                                            <div>
                                                <h5 class="fs-24 fw-600 text-white">For Professional</h5>
                                            </div>
                                            <div class="">
                                                <p class="fs-14 light-white m-0">Manage and Grow your Business</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <!-- Step 2 - Select Professional Type -->
                <fieldset class="step-2 stepTwo" style="display: none;">
                    <div class="row justify-content-center">
                        <div class="col-md-4 col-lg-4 text-center">
                            <a href="<?php echo e(route('home')); ?>" class="d-block mb-5">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/logo-white.svg" alt="icon">
                            </a>
                            <h3 class="mb-4 text-white fs-24">Sign Up for Vadu</h3>
                            <h5 class="mb-10 text-white fs-22">What type of Professional are You?</h5>
                            <div class="row gray-card-main justify-content-center">
                                <div class="col-md-12">
                                    <label class="w-100">
                                        <input type="radio" name="professional_type" value="contractor" required class="d-none">
                                        <div class="gray-card action-button role-selector h-100">
                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/login-customer-icon.svg" alt="">
                                            <div>
                                                <h5 class="fs-24 fw-600 text-white">Contractor</h5>
                                            </div>
                                            <div class="">
                                                <p class="fs-14 light-white m-0">Manage and Grow your Business</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            <button type="button" name="previous" class="previous step-two-prev-btn">
                                <span class="">
                                    <i class="fa-solid fa-chevron-left"></i>
                                </span>
                            </button>

                            <div class="row justify-content-center mt-5 g-3">
                                <div class="col-md-3">
                                    <input type="button" name="next" class="next btn-gradient" value="Next">
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <!-- Step 3 - Enter Email Address -->
                <fieldset class="step-3 glass-card stepThree">
                    <div class="row justify-content-center">
                        <div class="col-md-12 col-lg-12">
                            <div class="mb-10 text-center">
                                <a href="<?php echo e(route('home')); ?>" class="d-block mb-5">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/logo-white.svg" alt="icon">
                                </a>
                                <h3 id="role-heading"></h3>
                                <p class="fs-14" id="role-description"></p>
                            </div>

                            <div class="mb-8">
                                <div class="row">
                                    <div class="col-12 mb-4">
                                        <label class="fs-14 fw-500 text-start mb-3" for="email">Email Address</label>
                                        <input id="email" type="email" placeholder="Enter Email Address" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email" value="<?php echo e(old('email')); ?>">
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="invalid-feedback" role="alert"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="row justify-content-between g-3">
                                    <div class="col-md-12">
                                        <input type="button" name="next" class="next btn-gradient w-100" value="Continue">
                                    </div>
                                    <div class="col-md-12">
                                        <button type="button" name="previous" class="previous white-color btn w-100">
                                            <span class="ms-2">
                                                <i class="fa-solid fa-chevron-left"></i>
                                            </span>
                                            Back
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>

                <!-- Step 4 - Verify OTP -->
                <fieldset class="step-4 glass-card stepFour">
                    <div class="row justify-content-center">
                        <div class="col-md-12 col-lg-12">
                            <div class="mb-10 text-center">
                                <a href="<?php echo e(route('home')); ?>" class="d-block mb-5">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/logo-white.svg" alt="icon">
                                </a>
                                <h3>Verify your Password</h3>
                                <p class="fs-14">Please enter your password to log in as a customer.</p>
                            </div>

                            <div class="mb-8">
                                <div class="row">
                                    <div class="col-12 mb-4">
                                        <label class="fs-14 fw-500 text-start mb-3" for="otp">Enter Password</label>
                                        <div class="position-relative">
                                                <input id="password" type="password" placeholder="Enter Password" class="toggle-text form-control" name="password">
                                        <span id="" class="toggle-password btn-sm btn-icon position-absolute translate-middle end-0 pe-2 top-50">
                                            <i class="fa-solid fa-eye"></i>
                                            <i class="fa-solid fa-eye-slash d-none"></i>
                                        </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <input type="button" name="next" class="next btn-gradient w-100" value="Login">
                                    </div>
                                    <div class="col-md-12">
                                        <button type="button" name="previous" class="previous white-color btn w-100">
                                            <span class="ms-2">
                                                <i class="fa-solid fa-chevron-left"></i>
                                            </span>
                                            Back
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </fieldset>

                <!-- Step 5 - Verify OTP -->
                <fieldset class="step-5 glass-card stepFive">
                    <div class="row justify-content-center">
                        <div class="col-md-12 col-lg-12">
                            <div class="mb-10 text-center">
                                <a href="<?php echo e(route('home')); ?>" class="d-block mb-5">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/logo-white.svg" alt="icon">
                                </a>
                                <h3>Verify your Identity</h3>
                                <p class="fs-14">To Protect your Account, We'll Send a Message with a 6-Digit Code to Verify your Email Address.</p>
                            </div>

                            <div class="mb-8">
                                <div class="row">
                                    <div class="col-12 mb-4 position-relative">
                                        <label class="fs-14 fw-500 text-start mb-3" for="otp">Enter OTP</label>
                                        <input id="otp" type="number" placeholder="Enter OTP" class="toggle-text form-control" name="otp">
                                        <span id="toggle-password-otp" class="toggle-password btn-sm btn-icon position-absolute translate-middle end-0 pe-2">
                                            <i class="fa-solid fa-eye"></i>
                                            <i class="fa-solid fa-eye-slash d-none"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <input type="button" name="next" class="next btn-gradient w-100" value="Verify OTP">
                                    </div>
                                    <div class="col-md-12">
                                        <button type="button" name="previous" class="previous white-color btn w-100">
                                            <span class="ms-2">
                                                <i class="fa-solid fa-chevron-left"></i>
                                            </span>
                                            Back
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </form>

            <div class="site_logo text-center">
                <a href="<?php echo e(route('home')); ?>">
                    <img src="<?php echo e(asset('website')); ?>/assets/images/logo-white-footer.svg" alt="icon">
                </a>
                <ul class="list-inline">
                    <li class="list-inline-item"> <a href="<?php echo e('terms'); ?>" class="text-white">Terms of Use</a></li>
                    <li class="list-inline-item"><a href="#!" class="text-white">Support</a></li>
                    <li class="list-inline-item"><a href="<?php echo e('privacy_policy'); ?>" class="text-white">Privacy policy</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        let selectedRole = null;
        let selectedProfessionalType = null;

        const stepOne = document.querySelector('.stepOne');
        const stepTwo = document.querySelector('.stepTwo');
        const stepThree = document.querySelector('.stepThree');
        const stepFour = document.querySelector('.stepFour');
        const stepFive = document.querySelector('.stepFive');


        const roleRadios = document.querySelectorAll('input[name="role"]');
        const professionalTypeRadios = document.querySelectorAll('input[name="professional_type"]');

        const roleHeading = document.querySelector('#role-heading');
        const roleDescription = document.querySelector('#role-description');

        const nextButtons = document.querySelectorAll('.next');
        const previousButtons = document.querySelectorAll('.previous');

        // Function to Update Role Heading and Description
        function updateRoleText() {
            if (selectedRole === "customer") {
                roleHeading.textContent = 'Vadu for Customer';
                roleDescription.textContent = 'Create an Account to Get your Work Done.';
            } else if (selectedRole === "professional") {
                roleHeading.textContent = 'Vadu for Professional';
                roleDescription.textContent = 'Create an Account to Manage your Business.';
            }
        }

        // Function to handle navigation between steps
        function navigateToStep(currentStep, targetStep) {
            currentStep.style.display = 'none';
            targetStep.style.display = 'block';

            if (targetStep === stepThree) {
                updateRoleText();
            }
        }

        // Automatically proceed when a role is selected
        roleRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                selectedRole = this.value;

                if (selectedRole === "professional") {
                    // For professionals, go to step 2
                    navigateToStep(stepOne, stepTwo);
                } else if (selectedRole === "customer") {
                    // For customers, skip to step 3
                    selectedProfessionalType = null;
                    navigateToStep(stepOne, stepThree);
                }
            });
        });

        // Automatically proceed when a professional type is selected
        professionalTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                selectedProfessionalType = this.value;
                navigateToStep(stepTwo, stepThree);
            });
        });

        // Inside your existing nextButtons.forEach loop, add this condition:
        nextButtons.forEach(button => {
            button.addEventListener('click', () => {
                const currentStep = button.closest('fieldset');

                if (currentStep === stepOne) {
                    if (selectedRole === "professional") {
                        navigateToStep(stepOne, stepTwo);
                    } else if (selectedRole === "customer") {
                        navigateToStep(stepOne, stepThree);
                    }
                } else if (currentStep === stepTwo) {
                    if (selectedProfessionalType) {
                        navigateToStep(stepTwo, stepThree);
                    }
                } else if (currentStep === stepThree) {
                    navigateToStep(stepThree, stepFour);
                } else if (currentStep === stepFour) {
                    navigateToStep(stepFour, stepFive);
                }
                // Add similar logic for other steps if needed
            });
        });

        // Also update your previousButtons loop to handle Step 5 going back to Step 4:
        previousButtons.forEach(button => {
            button.addEventListener('click', () => {
                const currentStep = button.closest('fieldset');

                if (currentStep === stepTwo) {
                    navigateToStep(stepTwo, stepOne);
                } else if (currentStep === stepThree) {
                    if (selectedRole === "customer") {
                        navigateToStep(stepThree, stepOne);
                    } else if (selectedRole === "professional") {
                        navigateToStep(stepThree, stepTwo);
                    }
                } else if (currentStep === stepFour) {
                    navigateToStep(stepFour, stepThree);
                } else if (currentStep === stepFive) {
                    navigateToStep(stepFive, stepFour);
                }
            });
        });
    });

    $(document).ready(function() {
        const $toggleBtn = $('#toggle-password');
        const $passwordField = $('#password');
        const $eyeIcon = $toggleBtn.find('.fa-eye');
        const $eyeSlashIcon = $toggleBtn.find('.fa-eye-slash');

        $toggleBtn.on('click', function() {
            const isPassword = $passwordField.attr('type') === 'password';

            $passwordField.attr('type', isPassword ? 'text' : 'password');
            $eyeIcon.toggleClass('d-none', !isPassword);
            $eyeSlashIcon.toggleClass('d-none', isPassword);

            $toggleBtn.attr('aria-label', isPassword ? 'Hide password' : 'Show password');
        });
    });

    $(document).ready(function() {
        $('.toggle-password').on('click', function() {
            var passwordField = $('.toggle-text');
            var passwordFieldType = passwordField.attr('type');

            if (passwordFieldType === 'password') {
                passwordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                passwordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views/auth/register.blade.php ENDPATH**/ ?>