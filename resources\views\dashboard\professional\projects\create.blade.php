@extends('theme.layout.master')

@section('content')

<form action="" method="POST" enctype="multipart/form-data">
    @csrf
    <section class="">
        <div class="container">
            <div class="row">
                <div class="col-12 mb-10">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="page_title">
                            <h1>Add New Project</h1>
                            <p>My Projects <span><i class="fa-solid fa-angle-right"></i></span> Add New Project</p>
                        </div>
                        <button type="submit" class="btn_purple">Add</button>
                    </div>
                </div>
            </div>
            <div class="row g-3">

                <div class="col-md-6">
                    <label for="service_name" class="form-label">Project Title</label>
                    <input type="text" name="service_name" class="form-control @error('service_name') is-invalid @enderror"
                        id="service_name" value="{{ old('service_name') }}" placeholder="Enter your service name">
                    @error('service_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="tags" class="form-label">Sub-category</label>
                    <select name="tags[]" class="form-control form-select @error('tags') is-invalid @enderror"
                        id="tags">
                        <option>Tag1</option>
                        <option>Tag2</option>
                        <option>Tag3</option>
                        <option>Tag4</option>
                    </select>
                    @error('tags')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="location" class="form-label">Service Overview</label>
                    <input type="text" class="form-control @error('location') is-invalid @enderror"
                        name="location" id="location" value="{{ old('location') }}" placeholder="Enter your location">
                    @error('location')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-6">
                    <label for="email" class="form-label">Overall Cost</label>
                    <input type="email" name="email" class="form-control @error('email') is-invalid @enderror"
                        id="email" value="{{ old('email') }}" placeholder="Enter your email">
                    @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            <div class="row mt-3" id="image-gallery">
                <div class="col-md-3">
                    <label for="file1" class="form-label">Service Images</label>
                    <input name="file1" type="file" class="dropify"
                        data-height="126"
                        data-allowed-file-extensions="png jpg jpeg" />
                </div>
            </div>

        </div>
    </section>
</form>

@endsection

@push('js')
<script>
    $(document).ready(function() {
        $('.dropify').dropify({
            messages: {
                'default': 'Click to upload or drag and drop',
                'replace': 'Click to upload or drag and drop',
                'remove': 'Remove',
                'error': 'Ooops, something wrong happened.'
            },
            error: {
                'fileSize': 'The file size is too big (max 2MB).',
                'imageFormat': 'The image format is not allowed (PNG, JPG only).',
                'imageWidth': 'The image width must be under 800px.',
                'imageHeight': 'The image height must be under 400px.'
            }
        });

    $('.dropify').on('change', function(event) {
        var fileInput = $(this);
        var file = fileInput[0].files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var imageUrl = e.target.result;
                var deleteButton = `<button type="button" class="delete-image"><i class="fa-regular fa-trash-can"></i></button>`;
                var imageHtml = `
                    <div class="col-md-3 mb-3 image-item">
                        <img src="${imageUrl}" class="img-fluid" alt="Service Image">
                        ${deleteButton}
                    </div>
                `;
                $('#image-gallery').append(imageHtml);
            };
            reader.readAsDataURL(file);
        }
    });

    $('#image-gallery').on('click', '.delete-image', function() {
        $(this).closest('.image-item').remove();
    });
    });
</script>
@endpush