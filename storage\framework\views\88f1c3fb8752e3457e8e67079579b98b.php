<!-- Button to trigger modal -->
<!-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#projectModal">
    Open Project Modal
</button> -->

<!-- Modal -->
<div class="modal fade project-modal" id="projectModal" tabindex="-1" aria-labelledby="projectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content project-modal-parent">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="projectModalLabel">Project Card</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="project-modal-header p-0 d-flex justify-content-between align-items-center gap-2">
                    <div class="pm-boder-right d-flex gap-3 align-items-center">
                        <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" alt="logo" width="187" height="125">
                        <div class="modal-body-content">
                            <p class="text-midnight-navy mb-3">Build that dream patio</p>
                            <div class="project-modal-logo d-flex gap-2">
                                <img src="<?php echo e(asset('website/assets/media/images/card-sec-logo.svg')); ?>" alt="logo" width="31" height="31">
                                <div class="project-modal-logo-text">
                                    <h6 class="text-midnight-navy fs-12 fw-600">Summit Engineering</h6>
                                    <div class="d-flex align-items-center fs-12">
                                        <i class="fas fa-star text-black"></i>
                                        <p class="ms-1 fs-12 text-dark-black fw-600 mb-0">5.0</p>
                                        <p class="ms-1 fs-12 reddish-orange fw-600 mb-0">(546)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right side content (Starts From) -->
                    <div class="project-modal-sf d-flex flex-column align-items-end p-3">
                        <p class="dusk-blue fw-500 fs-12 mb-1">Starts From</p>
                        <p class="text-midnight-navy fw-700 mb-0">$457</p>
                    </div>
                </div>

                <form id="projectForm">
                    <div class="mb-3 mt-5">
                        <textarea type="text" class="form-control fs-14" id="fullName" name="fullName" placeholder="Describe the type of work you need done and be as specific as you want to be" rows="6"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="fullName" class="fs-12 fw-500 text-midnight-slate form-label">Full Name</label>
                        <input type="text" class="form-control" id="fullName" name="fullName" placeholder="Enter your full name">
                    </div>
                    <div class="pmf-inner d-flex gap-3">
                        <div class="mb-3 w-100">
                            <label for="email" class="fs-12 fw-500 text-midnight-slate form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email">
                        </div>
                        <div class="mb-3 w-100">
                            <label for="phone" class="fs-12 fw-500 text-midnight-slate form-label">Phone Number <span class="dusk-blue">(Optional)</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="Enter your phone number">
                        </div>
                    </div>
                    <div class="pmf-inner-select d-flex gap-3">
                        <div class="mb-3 w-100">
                            <label for="budget" class="fs-12 fw-500 text-midnight-slate form-label">Budget in mind</label>
                            <select class="form-select select2" id="budget" name="budget">
                                <option value="">Select your budget</option>
                                <option value="100-500">$100 - $500</option>
                                <option value="500-1000">$500 - $1,000</option>
                                <option value="1000-5000">$1,000 - $5,000</option>
                                <option value="5000+">$5,000+</option>
                            </select>
                        </div>
                        <div class="mb-3 w-100">
                            <label for="timeline" class="fs-12 fw-500 text-midnight-slate form-label">How soon are you looking to start the project?</label>
                            <select class="form-select select2" id="timeline" name="timeline">
                                <option value="">Select timeline</option>
                                <option value="immediately">Immediately</option>
                                <option value="1-2 weeks">Within 1-2 weeks</option>
                                <option value="1 month">Within 1 month</option>
                                <option value="flexible">Flexible timeline</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn-gridient fs-16 fw-500 mb-5 text-white w-100 mt-3">Let's talk</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('js'); ?>


<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            width: '100%',
            placeholder: $(this).data('placeholder'),
            minimumResultsForSearch: Infinity // hides search box for short lists
        });

        // Handle form submission
        $('#projectForm').on('submit', function(e) {
            e.preventDefault();
            // Add your form submission logic here
            alert('Form submitted!');
            $('#projectModal').modal('hide');
        });
    });
</script>
<?php $__env->stopPush(); ?><?php /**PATH D:\Github\vadu\resources\views\website\templates\Modal\project-card.blade.php ENDPATH**/ ?>