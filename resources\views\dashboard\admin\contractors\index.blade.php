@extends('theme.layout.master')

@section('content')

@if (Auth::user()->hasRole('admin'))

<section class="contractors">
    <div class="container">
        <div class="col-12">
            <div class="container">
                <ul class="nav nav-tabs page_tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link active" href="#tab1" data-bs-toggle="tab" role="tab">All Contractors</a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" href="#tab2" data-bs-toggle="tab" role="tab">Contractor Requests</a>
                    </li>
                </ul>

                <div class="tab-content">
                    <div class="tab-pane fade show active" id="tab1" role="tabpanel">
                        <div class="row py-4">
                            <div class="col-md-12">
                                <div class="table_container">
                                    <div class="table_header">
                                        <div class="search_container m-0">
                                            <i class="fas fa-search search_icon"></i>
                                            <input type="text" id="customSearch" placeholder="Search..." class="form-control">
                                        </div>
                                        <button type="button" class="btn_white"> <span><img src="{{asset('website')}}/assets/images/icon-filter.svg"></span> Filter</button>
                                    </div>


                                    <table id="myTable" class="display table table-borderless">
                                        <thead>
                                            <tr>
                                                <th><input type="checkbox" id="selectAll"></th>
                                                <th>Name</th>
                                                <th>Current Plan</th>
                                                <th>Email address</th>
                                                <th>Service</th>
                                                <th>Status</th>
                                                <th>Joined Date</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for ($i=0; $i<=10; $i++)

                                                <tr>
                                                <td><input type="checkbox" class="rowCheckbox"></td>
                                                <td>
                                                    <div class="profile_card">
                                                        <img class="rounded-circle" src="{{asset('website')}}/assets/images/avatar1.png" alt="user avatar" width="40">
                                                        <div class="profile_info">
                                                            <div class="name">Demi Wilkinson</div>
                                                            <div class="rating">
                                                                <i class="fas fa-star star"></i>
                                                                <span class="rating_text">5.0</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>Pro, $49</td>
                                                <td><span class="email_text"><EMAIL></span></td>
                                                <td>Flooring Installation</td>
                                                <td>
                                                    <span class="status_badge status_active">
                                                        <span class="status_dot"></span>
                                                        Active
                                                    </span>
                                                </td>
                                                <td><span class="date_text">Jan 5, 2025</span></td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button
                                                            class="drop-btn"
                                                            type="button"
                                                            id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown"
                                                            aria-expanded="false">
                                                            <i
                                                                class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul
                                                            class="dropdown-menu"
                                                            aria-labelledby="dropdownMenuButton">
                                                            <li>
                                                                <a
                                                                    href="https://www.google.com"
                                                                    class="dropdown-item complete fs-14 regular">
                                                                    Add
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a
                                                                    href="https://www.google.com"
                                                                    class="dropdown-item cancel fs-14 regular">
                                                                    Edit
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                                </tr>
                                                <tr>
                                                    <td><input type="checkbox" class="rowCheckbox"></td>
                                                    <td>
                                                        <div class="profile_card">
                                                            <img class="rounded-circle" src="{{asset('website')}}/assets/images/avatar2.png" alt="user avatar" width="40">
                                                            <div class="profile_info">
                                                                <div class="name">Candice Wu</div>
                                                                <div class="rating">
                                                                    <i class="fas fa-star star"></i>
                                                                    <span class="rating_text">5.0</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>Free</td>
                                                    <td><span class="email_text"><EMAIL></span></td>
                                                    <td>Painting</td>
                                                    <td>
                                                        <span class="status_badge status_inactive">
                                                            <span class="status_dot"></span>
                                                            Inactive
                                                        </span>
                                                    </td>
                                                    <td><span class="date_text">Jan 5, 2025</span></td>
                                                    <td>
                                                        <div class="dropdown">
                                                            <button
                                                                class="drop-btn"
                                                                type="button"
                                                                id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <i
                                                                    class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul
                                                                class="dropdown-menu"
                                                                aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <a
                                                                        href="https://www.google.com"
                                                                        class="dropdown-item complete fs-14 regular">
                                                                        Add
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a
                                                                        href="https://www.google.com"
                                                                        class="dropdown-item cancel fs-14 regular">
                                                                        Edit
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endfor

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="tab2" role="tabpanel">
                        <div class="row py-4">
                            <div class="col-md-12">
                                <div class="table_container">
                                    <div class="table_header">
                                        <div class="search_container m-0">
                                            <i class="fas fa-search search_icon"></i>
                                            <input type="text" id="customSearch" placeholder="Search..." class="form-control">
                                        </div>
                                        <button type="button" class="btn_white"> <span><img src="{{asset('website')}}/assets/images/icon-filter.svg"></span> Filter</button>
                                    </div>


                                     <table id="myTable" class="display table table-borderless">
                                        <thead>
                                            <tr>
                                                <th><input type="checkbox" id="selectAll"></th>
                                                <th>Name</th>
                                                <th>Email address</th>
                                                <th>Service</th>
                                                <th>Status</th>
                                                <th>Request Date</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for ($i=0; $i<=10; $i++)

                                                <tr>
                                                <td><input type="checkbox" class="rowCheckbox"></td>
                                                <td>
                                                    <div class="profile_card">
                                                        <img class="rounded-circle" src="{{asset('website')}}/assets/images/avatar1.png" alt="user avatar" width="40">
                                                        <div class="profile_info">
                                                            <div class="name">Demi Wilkinson</div>
                                                            <div class="rating">
                                                                <i class="fas fa-star star"></i>
                                                                <span class="rating_text">5.0</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="email_text"><EMAIL></span></td>
                                                <td>Flooring Installation</td>
                                                <td>
                                                    <span class="status_badge status_active">
                                                        <span class="status_dot"></span>
                                                        Approved
                                                    </span>
                                                </td>
                                                <td><span class="date_text">Jan 5, 2025</span></td>
                                                <td>
                                                    <div class="d-flex gap-2 action_btn">
                                                            <button class="approve_btn">
                                                                <img src="{{asset('website')}}/assets/images/icon-check.svg" alt="approve-icon">
                                                            </button>
                                                            <button class="delete_btn">
                                                                <img src="{{asset('website')}}/assets/images/icon-x.svg" alt="reject-icon">
                                                            </button>
                                                        </div>
                                                </td>
                                                </tr>
                                                <tr>
                                                    <td><input type="checkbox" class="rowCheckbox"></td>
                                                    <td>
                                                        <div class="profile_card">
                                                            <img class="rounded-circle" src="{{asset('website')}}/assets/images/avatar2.png" alt="user avatar" width="40">
                                                            <div class="profile_info">
                                                                <div class="name">Candice Wu</div>
                                                                <div class="rating">
                                                                    <i class="fas fa-star star"></i>
                                                                    <span class="rating_text">5.0</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><span class="email_text"><EMAIL></span></td>
                                                    <td>Painting</td>
                                                    <td>
                                                        <span class="status_badge status_inactive">
                                                            <span class="status_dot"></span>
                                                            Inactive
                                                        </span>
                                                    </td>
                                                    <td><span class="date_text">Jan 5, 2025</span></td>
                                                    <td>
                                                        <div class="d-flex gap-2 action_btn">
                                                            <button type="button" class="approve_btn">
                                                                <img src="{{asset('website')}}/assets/images/icon-check.svg" alt="approve-icon">
                                                            </button>
                                                            <button type="button" class="reject_btn">
                                                                <img src="{{asset('website')}}/assets/images/icon-x.svg" alt="reject-icon">
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endfor

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>






@endif

@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
@endpush