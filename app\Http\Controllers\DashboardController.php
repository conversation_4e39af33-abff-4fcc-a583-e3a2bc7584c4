<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }

    public function dashboard()
    {
        // return view('dashboard.index');
        return view('theme.index');
    }

    public function professional_account()
    {
        return view('auth.professional_account_stepper');
    }

    public function contractors()
    {
        return view('dashboard.admin.contractors.index');
    }

    public function contractorProfile()
    {
        return view('dashboard.admin.contractors.profile');
    }

    public function titleEscrow()
    {
        return view('dashboard.admin.title-escrow.index');
    }

    public function titleEscrowCreate()
    {
        return view('dashboard.admin.title-escrow.create');
    }

    public function titleEscrowEdit()
    {
        return view('dashboard.admin.title-escrow.edit');
    }

    public function mortgageLenders()
    {
        return view('dashboard.admin.mortgage-lenders.index');
    }

    public function mortgageLendersCreate()
    {
        return view('dashboard.admin.mortgage-lenders.create');
    }

    public function mortgageLendersEdit()
    {
        return view('dashboard.admin.mortgage-lenders.edit');
    }

    public function serviceCategories()
    {
        return view('dashboard.admin.service-categories.index');
    }

    public function serviceCategoriesCreate()
    {
        return view('dashboard.admin.service-categories.create');
    }

    public function subscription()
    {
        return view('dashboard.admin.subscription');
    }

    public function profileSettings()
    {
        return view('dashboard.profile_settings');
    }

    public function notifications()
    {
        return view('dashboard.notifications');
    }

    // professional account pages

    public function projects()
    {
        return view('dashboard.professional.projects.index');
    }
    public function projectsCreate()
    {
        return view('dashboard.professional.projects.create');
    }

    public function quoteRequest()
    {
        return view('dashboard.professional.quote-request.index');
    }
}
