<section class="hero-section banner-sec d-flex flex-column justify-content-center text-center">
    <div class="container">
        <div class="row align-items-center banner-content">
            <div class="col-lg-12">
                <h1 class="section-title fw-400">
                    <?php echo e($pageTitle ?? 'Default Banner Heading'); ?>

                </h1>
                <div class="breadcrumbs">
                    <?php if(isset($breadcrumbs) && count($breadcrumbs) > 0): ?>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb justify-content-center">
                                <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($loop->last): ?>
                                        <li class="breadcrumb-item active" aria-current="page">
                                            <?php echo e($breadcrumb['title']); ?>

                                        </li>
                                    <?php else: ?>
                                        <li class="breadcrumb-item">
                                            <a href="<?php echo e($breadcrumb['url']); ?>"><?php echo e($breadcrumb['title']); ?></a>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ol>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<?php /**PATH D:\Github\vadu\resources\views\website\templates\banner.blade.php ENDPATH**/ ?>