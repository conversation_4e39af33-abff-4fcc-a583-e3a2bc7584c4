<?php $__env->startSection('content'); ?>

<section class="hero-section d-flex flex-column justify-content-center text-center">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-12 custom-grid-sec">
                <h1 class="section-title fw-400">Get connected with the Top-rated Professional and <span class="text-burnt-peach">Build that dream patio</span></h1>
                <div class="search-wrapper mb-5 mt-5">
                    <div class="search-container d-flex justify-content-between align-items-center ">
                        <div class="search-container-input d-flex gap-3 align-items-center">
                            <div class="search-input-section d-flex gap-3 align-items-center">
                                <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <input type="text" class="search-input fs-16" placeholder="I need a" data-type="search">
                            </div>
                            <div class="vertical-divider"></div>
                            <div class="location-section d-flex gap-3 align-items-center">
                                <svg class="location-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <input type="text" class="location-input fs-16" placeholder="In What Area?" data-type="location">
                            </div>
                        </div>
                        <button class="search-btn btn-gridient" data-action="search">
                            <svg class="search-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-12 mt-5 pt-5 position-relative">
                <div class="stats-container">
                    <div class="header">
                        <div class="verified-users d-flex justify-content-center">
                            <div class="verified-users-inner d-flex align-items-center">
                                <div class="user-avatars">
                                    <div class="avatar">J</div>
                                    <div class="avatar">M</div>
                                    <div class="avatar">A</div>
                                </div>
                                <p class="fs-15 fw-500">623+ Verified Users</p>
                            </div>
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <h2 class="stat-number text-jet-black mb-2">215,292+</h2>
                            <p class="stat-label medium-gray">Verified Professionals</p>
                        </div>

                         <div class="stat-item">
                            <h2 class="stat-number text-jet-black mb-2">215,292+</h2>
                            <p class="stat-label medium-gray">Verified Professionals</p>
                        </div>

                          <div class="stat-item">
                            <h2 class="stat-number text-jet-black mb-2">215,292+</h2>
                            <p class="stat-label medium-gray">Verified Professionals</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="services-section py-5 px-3">
    <div class="container">
        <div class="row text-center mb-5 gy-4">
            <!-- Heading Section -->
            <div class="col-lg-12">
                <h4 class="fw-700 text-midnight-navy">
                    What can we do <span class="text-gridient">for you?</span>
                </h4>
                <p class="text-dusk-blue fs-16 w-50 m-auto mb-5">
                    Service categories help organize and structure the offerings on a marketplace, making it
                    easier for users to find what they need.
                </p>
            </div>

            <!-- Services Grid -->
            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/bath-remodeling-icon.svg')); ?>" width="40" height="40" alt="Bathroom Remodeling Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Bathroom Remodeling</h6>
                    <a href="#" class="fs-14 fw-500 reddish-orange">View All</a>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/kitchen-remodeling.svg')); ?>" width="40" height="40" alt="Kitchen Remodeling Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Kitchen Remodeling</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">162 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/basement-finishing.svg')); ?>" width="40" height="40" alt="Basement Finishing Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Basement Finishing</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">897 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/flooring-installation.svg')); ?>" width="40" height="40" alt="Flooring Installation Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Flooring Installation</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">265 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/Painting.svg')); ?>" width="40" height="40" alt="Painting Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Painting</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">894 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/drywall-wall-repair.svg')); ?>" width="40" height="40" alt="Drywall Repair Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Drywall / Wall Repair</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">9,874 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/Electrical.svg')); ?>" width="40" height="40" alt="Lighting & Electrical Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Lighting & Electrical</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">684 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/Electrical.svg')); ?>" width="40" height="40" alt="Roofing Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Roofing</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">468 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/siding-installation.svg')); ?>" width="40" height="40" alt="Siding Installation Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Siding Installation</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">146 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/patio.svg')); ?>" width="40" height="40" alt="Deck or Patio Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Deck or Patio</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">2648 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/window-door.svg')); ?>" width="40" height="40" alt="Window & Door Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Window & Door</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">642 Professionals</p>
                </div>
            </div>

            <div class="col-md-2 col-sm-4">
                <div class="service-card">
                    <img src="<?php echo e(asset('website/assets/media/images/refinancing.svg')); ?>" width="40" height="40" alt="Refinancing Icon" class="service-icon mb-3">
                    <h6 class="fs-14 fw-600 mb-2">Refinancing</h6>
                    <p class="fs-14 fw-400 text-dusk-blue">1,264 Professionals</p>
                </div>
            </div>

            <div class="col-12 text-center">
                <a href="#" class="browse-link fs-14 fw-600 text-gridient">
                    Browse all services <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Featured Professionals Section -->
<section class="featured-sec bg-linear py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class=""><span class="text-gridient">VADU</span> Featured Professionals</h2>
                <p class="text-dusk-blue">Each listing is designed to be clear and concise, providing customers</p>
            </div>
        </div>

        <div class="row">
            <div class="col-12 position-relative">
                <!-- Outer Swiper -->
                <div class="swiper professionalSwiper">
                    <div class="swiper-wrapper">
                        <?php for($i = 0; $i < 9; $i++): ?>
                            <div class="swiper-slide">
                            <div class="professional-card bg-white rounded shadow-sm overflow-hidden">
                                <!-- Inner Image Swiper -->
                                <div class="swiper myimageSwiper">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="position-relative">
                                                <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" class="img-fluid w-100" alt="Professional">
                                                <button class="wishlist-btn">
                                                    <i class="far fa-heart text-muted"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="position-relative">
                                                <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" class="img-fluid w-100" alt="Professional">
                                                <button class="wishlist-btn">
                                                    <i class="far fa-heart text-muted"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="position-relative">
                                                <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" class="img-fluid w-100" alt="Professional">
                                                <button class="wishlist-btn">
                                                    <i class="far fa-heart text-muted"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-pagination"></div>
                                </div>

                                <!-- Card Content -->
                                <div class="p-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="text-midnight-navy fw-600 mb-1">Acme Engineering Services</h6>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-star text-black"></i>
                                                <p class="ms-1 text-dark-black fw-600">5.0</p>
                                                <p class="ms-1 reddish-orange fw-600">(546)</p>
                                            </div>
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <span class="fs-15 fw-500 dusk-blue">Great Falls, Maryland</span>
                                            </div>
                                        </div>
                                        <div class="ms-3">
                                            <div class="aes_logo" style="width: 40px; height: 40px;">
                                                <img class="img-fluid" src="<?php echo e(asset('website/assets/media/images/card-engineering-logo.svg')); ?>" alt="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <?php if($i % 3 == 0): ?>
                                        <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">Contractor</span>
                                        <?php elseif($i % 3 == 1): ?>
                                        <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">Title & Escrow</span>
                                        <?php else: ?>
                                        <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">Mortgage Lenders</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                    </div>
                    <?php endfor; ?>
                </div>
            </div>
            <!-- Custom professional Next Button -->
            <button class="professional-next swiper-button-next position-absolute top-50 end-0 translate-middle-y bg-white rounded-circle shadow-sm border-0 p-2" style="z-index: 10; width: 40px; height: 40px;"></button>

        </div>
    </div>


    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="#" class="browse-link fs-14 fw-600 text-gridient">
                Browse all services <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
    </div>
</section>

<section class="trending-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 pb-5">
                <h4 class="fw-700 text-midnight-navy text-center">Trending <span class="text-gridient">Services</span></h4>
                <p class="text-dusk-blue fs-16 text-center">
                    Each listing is designed to be clear and concise, providing customers
                </p>
            </div>
            <div class="col-lg-12 position-relative">
                <div class="swiper trendingSwiper pb-5 pt-3">
                    <div class="swiper-wrapper">
                        <?php for($i = 0; $i < 8; $i++): ?>
                            <div class="swiper-slide">
                            <div class="professional-card bg-white rounded shadow-sm overflow-hidden">
                                <!-- Swiper -->
                                <div class="swiper myimageSwiper">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="position-relative">
                                                <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" class="img-fluid w-100" alt="Professional">
                                                <button class="wishlist-btn">
                                                    <i class="far fa-heart text-muted"></i>
                                                </button>
                                                <div class="top-rated">
                                                    <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">TOP RATED</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-3">
                                    <h6 class="text-midnight-navy fs-16 fw-600 mb-3">Build that dream patio</h6>

                                    <div class="d-flex align-items-center gap-2">
                                        <div class="" style="width: 30px; height: 30px;">
                                            <img class="img-fluid" src="<?php echo e(asset('website/assets/media/images/card-sec-logo.svg')); ?>" alt="">
                                        </div>
                                        <div>
                                            <h6 class="text-midnight-navy fs-12 fw-600">Summit Engineering</h6>
                                            <div class="d-flex align-items-center fs-12">
                                                <i class="fas fa-star text-black"></i>
                                                <p class="ms-1 fs-12 text-dark-black fw-600">5.0</p>
                                                <p class="ms-1 fs-12 reddish-orange fw-600">(546)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">Contractor</span>
                                    </div>
                                </div>
                                <div class="card_footer d-flex justify-content-between ">
                                    <div class="star_from">
                                        <p class="fs-14 dusk-blue fw-500"> Start From</p>
                                        <p class="fs-17 fw-700 text-midnight-navy">$100</p>
                                    </div>
                                    <a href="service-detail" class="btn btn-quote text-white fs-13 fw-600">Get Quote</a>
                                </div>
                            </div>
                    </div>
                    <?php endfor; ?>
                </div>
            </div>
            <!-- Custom trending Next Button -->
            <button class="trending-next swiper-button-next position-absolute top-50 end-0 translate-middle-y bg-white rounded-circle shadow-sm border-0 p-2" style="z-index: 10; width: 40px; height: 40px;"></button>
        </div>
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="#" class="browse-link fs-14 fw-600 text-gridient">
                    Browse all services <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    </div>
</section>


<!-- Testimonials Section -->
<section class="testimonials-sec py-20">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="swiper testimonialSwiper p-5">
                    <div class="swiper-wrapper">
                        <?php for($i = 0; $i < 4; $i++): ?>
                            <div class="swiper-slide">
                            <div class="testimonial-parent d-flex gap-5">
                                <div class="testimonial-inner d-flex flex-column justify-content-center gap-8">
                                    <img src="<?php echo e(asset('website/assets/media/images/testimoials-quote.svg')); ?>" alt="" width="28px" height="21px">
                                    <p class="fs-28 text-white">
                                        Lorem ipsum dolor sit amet consectetur. Ipsum nibh adipiscing purus accumsan accumsan ullamcorper ut. Pellentesque ac eget praesent in sapien tellus sed sed interdum. Sapien viverra et molestie neque sit morbi.
                                    </p>
                                    <div class="testimonal-child">
                                        <h5 class="fw-700 text-white">Lincoln Donin</h5>
                                        <h6 class="fw-400 light-white">VP of Marketing, JobSync</h6>
                                    </div>
                                </div>
                                <img src="<?php echo e(asset('website/assets/media/images/user-image.png')); ?>" width="350px" height="350px" alt="user image">
                            </div>
                    </div>
                    <?php endfor; ?>
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </div>
    </div>
</section>

<section class="why-vadu-sec pb-20 pt-7">
    <div class="container">
        <div class="row why-vadu-parent">
            <div class="col-lg-6 d-flex flex-column justify-content-center gap-5">
                <h3 class="fw-700 text-midnight-navy fw-700">Why VADU?</h3>
                <h6 class="text-dusk-blue fw-400">
                    Lorem ipsum dolor sit amet consectetur. In nunc massa viverra facilisi egestas volutpat tristique luctus porta. Viverra fringilla eros morbi pellentesque arcu. Sem odio consequat dui pretium sit. Viverra fringilla eros morbi pellentesque arcu. </h6>
                <div class="vadu-btn d-flex gap-4 mt-4">
                    <button href="#" class="btn-gridient fs-16 fw-500">Get Started</button>
                    <button href="#" class="learn-more-btn fs-16 fw-500">Learn More</button>
                </div>
            </div>
            <div class="col-lg-2 why-vadu-img">
                <img src="<?php echo e(asset('website/assets/media/images/why-vadu-first-image.png')); ?>" alt="why-vadu-img" width="100%" height="100%">
            </div>
            <div class="col-lg-4 why-vadu-img">
                <img src="<?php echo e(asset('website/assets/media/images/why-vadu-second-image.png')); ?>" alt="why-vadu-img" width="100%" height="100%">
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script>
    $(document).ready(function() {
        const $searchInput = $('.search-input');
        const $locationInput = $('.location-input');
        const $searchButton = $('.search-btn');

        $searchButton.on('click', function(e) {
            e.preventDefault();

            const searchTerm = $searchInput.val().trim();
            const location = $locationInput.val().trim();

            if (!searchTerm && !location) {
                $('.search-container').addClass('shake');
                setTimeout(() => {
                    $('.search-container').removeClass('shake');
                }, 300);
                return;
            }

            $searchButton.prop('disabled', true).addClass('loading').html(`
            <svg class="search-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
            </svg>
            Searching...
        `);

            setTimeout(() => {
                alert(`🔍 Searching for "${searchTerm || 'anything'}" in "${location || 'your area'}"`);
                $searchButton.prop('disabled', false).removeClass('loading').html(`
                <svg class="search-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Search
            `);
            }, 1500);
        });
    });
</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views/website/index.blade.php ENDPATH**/ ?>