@import url('https://fonts.googleapis.com/css2?family=Archivo:ital,wght@0,100..900;1,100..900&display=swap');


:root {
    --dark-black: #000;
    --white: #fff;
    --jet-black :#1A1A1A;
    --burnt-peach :#EC9B6E;
    --dusk-blue : #656B76;
    --midnight-navy :#032642;
    --midnight-slate :#242B3A;
    --pale-silver : #F6F7F7; 
    --reddish-orange : #f15a29;
    --cloud-gray :#EBECED;
    --purple: #9B6CD6;
    --accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    --ghost-white:#F4F5F5;
    --vivid-red:#FF0209;
    --medium-gray:#4D4D4D;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body{
    overflow-x: hidden;
}

/*Heading Font Sizes */
h1 {font-size: 60px; font-family: "Archivo", sans-serif;}
h2 {font-size: 50px; font-family: "Archivo", sans-serif;}
h3 {font-size: 48px; font-family: "Archivo", sans-serif;}
h4 {font-size: 32px; font-family: "Archivo", sans-serif;}
h5 {font-size: 24px; font-family: "Archivo", sans-serif;}
h6 {font-size: 20px; font-family: "Archivo", sans-serif;}
p {font-size: 18px; font-family: "Archivo", sans-serif;}

h1,h2,h3,h4,h5,h6 {line-height: 1.2;word-break: break-word;margin: 0px;}
p,a,span {line-height: 1.6;word-break: break-word;text-decoration: none;}
ul,ol {margin: 0px;padding: 0px;}
li {font-size: 16px; font-family: "Archivo", sans-serif;}
p {margin: 0px;}

/* Custom-Font-size */
.fs-38 {font-size: 38px; line-height: 1.2; font-family: "Archivo", sans-serif;}
.fs-28 {font-size: 28px; line-height: 1.2; font-family: "Archivo", sans-serif;}
.fs-22 {font-size: 22px; line-height: 1.2; font-family: "Archivo", sans-serif;}
.fs-17 {font-size: 17px; line-height: 1.2; font-family: "Archivo", sans-serif;}
.fs-16 {font-size: 16px; line-height: 1.2; font-family: "Archivo", sans-serif;}
.fs-15 {font-size: 15px; line-height: 1.2; font-family: "Archivo", sans-serif;}
.fs-14 {font-size: 14px; line-height: 1.6; font-family: "Archivo", sans-serif;}
.fs-12 {font-size: 12px; line-height: 1.2; font-family: "Archivo", sans-serif;}



/* custom-color-classes */
.vivid-red{color: var(--vivid-red);}
.text-dark-black {color: var(--dark-black);}
.text-white {color: var(--white);}
.text-jet-black {color: var(--jet-black);}
.text-burnt-peach {color: var(--burnt-peach);}
.text-dusk-blue {color: var(--dusk-blue);}
.text-midnight-navy {color: var(--midnight-navy);}
.text-midnight-slate {color: var(--midnight-slate);}
.pale-silver {background: var(--pale-silver);}
.reddish-orange{color: var(--reddish-orange);}
.dusk-blue{color: var(--dusk-blue);opacity: 0.6;}
.light-white{color: var(--white);opacity: 0.6;}
.medium-gray{color: var(--medium-gray);}
.text-gridient {width: fit-content; background: linear-gradient(90deg, var(--burnt-peach) 58.81%, #9D6FD9 80.66%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
/* bg-Linear-gridient */
.bg-linear {background: linear-gradient(0deg, rgba(255, 255, 255, 0.80) 0%, rgba(255, 255, 255, 0.80) 100%), linear-gradient(90deg, var(--burnt-peach) 48.36%, #9D6FD9 100%);}

.bg-pale-silver {background: var(--pale-silver);}
.bg-grey { background-color: #FBFBFB; }
.bg-white { background-color: #FFF; }

/* font-weight  */
.fw-700 {font-weight: 700;}
.fw-600{font-weight: 600;}
.fw-500{font-weight: 500;}  
.fw-400{font-weight: 400;}
.fw-300{font-weight: 300;}

/* Border-color Radius */
.border-dusk-blue {border: 1px solid var(--dusk-blue);}
.border-radius {border-radius: 50px;}
.br-16 { border-radius: 16px;}

.sticky { position: sticky; top: 20px;}

/* box-shadow */
.newsLettershadow {border-radius: 10px;background: #FBFBFB;box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.08);padding: 14.99px 15px 15px 15px; }

/* Global-Buttons */
.btn-gridient:hover {background: linear-gradient(120deg, #9d6fd9 40.36%, var(--burnt-peach) 100%);color:var(--white);}
.btn-gridient {font-family: Archivo; border-radius: 50px;border:0;background: linear-gradient(90deg, var(--burnt-peach) 48.36%, #9d6fd9 100%);color: white;padding: 13px 20px;transition: background 0.3s ease;}
.btn-login {padding: 13px 18px;}
/* Quote-Btn */
.btn-quote {border-radius: 6px;background: var(--midnight-navy);box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10) !important; display: flex;width: 160px;height: 36px;justify-content: center;align-items: center;}
.btn-quote:hover {color: var(--dark-black) !important;}
/* learn-more-btn */
.learn-more-btn {border-radius: 8px;border: 1px solid var(--midnight-navy);padding: 13px 30px;background-color: transparent;}
/* Get-Started-Btn */
.get-started-btn {border-radius: 8px;background: var(--white);padding: 13px 50px;border: 0;}

.box-shadow{padding: 20px 18px 20px 18px;border-radius: 8px;border: 1px solid var(--cloud-gray);background: var(--white);box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.25);}

/* Footer-Input */
input.form-control:focus {box-shadow: none;border: 1px solid #DCDCDC;}
input.form-control {height: 42px;padding: 13px 15px 13px 15px;border-radius: 8px;border: 1px solid #DCDCDC;background: var(--white);}

/* Footer */
footer.footer {padding-top: 13em;}
.listed-vadu-sec {margin-bottom: -11em;position: relative;}
.social-icons a img {transition: all 0.3s ease;}
.footer-bottom {border-top: 1px solid var(--cloud-gray);}
.social-icons {display: flex;align-items: center;gap: 10px;}
.copyright a:hover {text-decoration-line: underline !important;}
.social-icons a:hover {color: #1DA1F2; transform: scale(1.1);}
.social-icons i.fa-brands.fa-facebook {font-size: 30px;color: #1877F2;}
.social-icons a:hover img {filter: brightness(1.2);transform: scale(1.1);}
.copyright a {text-decoration-line: underline;color: var(--midnight-slate);}
.social-icons i.fa-brands.fa-youtube {background: #FF0209;padding: 7px 6px;color:var(--white);border-radius: 50px;font-size: 20px;}
.social-icons i.fa-brands.fa-whatsapp {background: #00E510;color: var(--white);padding: 7px 9px;font-size: 20px;border-radius: 50px;}
.social-icons i.fa-brands.fa-linkedin-in {background: #007EBB;color: var(--white);padding: 7px 9px;font-size: 20px;border-radius: 50px;}
.social-icons a {display: inline-flex;align-items: center;justify-content: center;font-size: 24px;color: var(--midnight-slate);transition: all 0.3s ease;}
.social-icons i.fa-brands.fa-instagram {font-size: 20px;background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);color: var(--white);padding: 7px 8px;border-radius: 50px;}


/* Header */
.vadu-header {border-radius: 100px;background: rgba(255, 255, 255, 0.54);box-shadow: 0px 2px 3px 0px rgba(183, 183, 183, 0.08), 0px 2px 2px 0px rgba(183, 183, 183, 0.10);padding: 10px 0;  margin: 15px 0;}
.container:has(.vadu-header) { position: fixed; margin: auto; left: 0; right: 0; z-index: 9999;/* max-width: calc(100vw - 45%); */ }
.vadu-header ul {overflow: hidden;}
.vadu-header ul li::marker {color: #D9D9D9; font-size: 20px; }
.vadu-header ul li {list-style: disc;}


/* .banner-sec { height: 300px;} */
.banner-sec .banner-content { padding-top: 50px; }

/* Banner end */


/* Services Section Styles */
.service-card {padding: 20px 15px;text-align: center;height: 100%;transition: all 0.3s ease;border-radius: 5px;border: 1px solid var(--cloud-gray);background: var(--white);}
.service-card:hover {border: 1px solid var(--reddish-orange);border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.25);transition: all 0.3s ease;background: linear-gradient(135deg, #ec9b6e2b, #ffffff57, #9d6fd91a);}
.browse-link {border-radius: 100px;border: 1px solid var(--cloud-gray);padding: 5px 15px 5px 15px;}
.aes_logo img {border-radius: 100px;border: 1px solid var(--cloud-gray);background: var(--white);padding: 5px 5px 5px 5px;}
.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after {font-size: 14px !important;color: var(--midnight-navy);}

.wishlist-btn {width: 31px;height: 31px;padding: 6px 6px 5px 5px;border-radius: 15.5px;background: #FFF;border: 0;position: absolute;top: 5px;right: 4px;}
.wishlist-btn i {line-height: unset;}
.wishlist-btn .fa-heart:before {position: absolute;top: 22%;left: 28%;}



/* Trending-service */
.top-rated span {position: absolute;top: 6px;left: 5px;}
.card_footer {background: rgba(241, 90, 41, 0.08); padding: 12px;}
.testimonial-parent {border-radius: 20px;border: 2px solid var(--burnt-peach);padding: 30px;    margin: 40px;}   
.professional-card {border-radius: 8px;border: 0.828px solid var(--cloud-gray);background: var(--white);box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.25);}
.testimonialSwiper .swiper-pagination-bullet-active {border-radius: 100px;background: linear-gradient(90deg, #EC9B6E 48.36%, #9D6FD9 100%), var( --midnight-navy);width: 36px;height: 10px;}
.testimonialSwiper:before {content: '';background-image: url(../media/images/testimonial-before.png);width: 739px;height: 522px;transform: rotate(25.276deg);position: absolute;top: -18%;background-repeat: no-repeat;background-size: contain;left: 73%;}
.testimonialSwiper:after {content: '';background-image: url(../media/images/testimonal-after.png);width: 739px;height: 522px;position: absolute;top: -22%;background-repeat: no-repeat;background-size: contain;left: -5%;border-radius: 1087500px;opacity: 0.8;}

/* Default bullet color */
.swiper-pagination-bullet {background-color:var(--white) !important;opacity: 1; width: 9px;height: 9px;}
/* Active bullet color */
.swiper-pagination-bullet-active {background-color: var(--white) !important; width: 9px;height: 9px;}

/* testimonial-slider */
.testimonialSwiper {border-radius: 22px;background: var(--midnight-navy);}


/* why-vadu-sec */
.why-vadu-parent {border-radius: 22px;border-top: 1px solid #E7E8EC; box-shadow: 0px -2px 2px 0px rgba(0, 0, 0, 0.05) inset, 0px 4px 2px 0px rgba(255, 255, 255, 0.25) inset, 4px 4px 26px 0px rgba(255, 255, 255, 0.25) inset;padding: 40px;}
.why-vadu-img img {border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.20);background: #F2E7E3;object-fit: cover;}
.vadu-btn .btn-gridient {border-radius: 8px;padding: 13px 30px;}

/* listed-vadu-banner */
.listed-vadu-inner {border-radius: 22px; background-image: url(../media/images/listed-vadu-banner.png);padding: 5em;}
.listed-vadu-btn .learn-more-btn {border-radius: 8px;border: 1px solid var(--cloud-gray);}


/* Contractors */

/* Filter start */

.filter-container { background: #fff; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);  } 
.filter-container .filter-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }

.filter-container .filter-title { font-size: 16px; font-weight: 600; color: #333; display: flex; align-items: center; gap: 8px; } 
.filter-container .filter-icon  { width: 16px; height: 16px; } 
.filter-container .reset-filter { color: #4A90E2; text-decoration: none; font-size: 14px; cursor: pointer; }

.filter-container .filter-item { margin-bottom: 20px; } 
.filter-container .form-label { display: block; font-weight: 600; color: #333; margin-bottom: 8px; font-size: 14px; }

.filter-container .search-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; color: #666; }
.filter-container .search-input::placeholder {  color: #999;}

.filter-container .filter-button { padding: 8px 16px; border: 1px solid #ddd; border-radius: 20px; background: #fff; cursor: pointer; font-size: 12px; transition: all 0.2s; } 
.filter-container .filter-button.active { background: #8B5FBF; color: #fff; border-color: #8B5FBF; }
.filter-container .filter-button:hover { border-color: #8B5FBF;}

.filter-container .services-list .service-item { display: flex; align-items: center; padding: 6px 0; font-size: 14px;}
.filter-container .services-list .service-item .service-checkbox { margin-right: 10px; width: 16px; height: 16px; accent-color: #8B5FBF;}

.filter-container .budget-section { margin-bottom: 20px;}

.filter-container .range-container { margin: 15px 0; position: relative;}
.filter-container .price-range { width: 100%; height: 6px; border-radius: 3px; background: linear-gradient(to right, #8B5FBF 0%, #8B5FBF 60%, #ddd 60%, #ddd 100%); outline: none; appearance: none; } 
.filter-container .price-range::-webkit-slider-thumb { appearance: none; width: 18px; height: 18px; border-radius: 50%; background: #8B5FBF; cursor: pointer; border: 2px solid #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.2); }
.filter-container .price-range::-moz-range-thumb { width: 18px; height: 18px; border-radius: 50%; background: #8B5FBF; cursor: pointer; border: 2px solid #fff;box-shadow: 0 2px 4px rgba(0,0,0,0.2);}

.filter-container .price-display { text-align: center; color: #666; font-size: 14px; margin-top: 10px; }

.filter-container .search-button { width: 100%; background: #2C3E50; color: #fff;  border: none;  padding: 10px 15px; border-radius: 6px;  font-size: 16px; font-weight: 600; cursor: pointer;  transition: background-color 0.2s;}
.filter-container .search-button:hover { background: #34495E;}

.filter-container .type-wrapper .type-label { padding: 5px 12px; border: 1px solid #ddd; border-radius: 20px; background: #fff; cursor: pointer; font-size: 12px; transition: all 0.2s;  }
.filter-container .type-wrapper input:checked + .type-label { background: #8B5FBF; color: #fff; border-color: #8B5FBF;  }

.trans-accordion .accordion-item, .trans-accordion .accordion-item button, .trans-accordion .accordion-button:not(.collapsed) { border: 0; padding-inline: 0;  box-shadow: none; background-color: transparent;}
.trans-accordion .accordion-item button:focus { box-shadow: none; outline: 0; }
.trans-accordion .accordion-item .accordion-body { padding-inline: 0;} 
.trans-accordion .filter-title { font-size: 18px; font-weight: 600; color: #000;}
.trans-accordion .accordion-button:not(.collapsed)::after { background-image: var(--accordion-btn-active-icon);}

/* Filter End*/

/* Sorting Start*/

.sorting-wrapper .view-change { border: 1.5px solid var(--cloud-gray); border-radius: 5px;}
.sorting-wrapper .view-change button.active { background-color: var(--purple);}
.sorting-wrapper .view-change button.active i { color: var(--white);}

/* Sorting End*/


/* Service Detail Start */

.listing-detail .listing-content .card { padding: 25px;}
.listing-detail .listing-content .card .customer-review .card-review { padding: 25px;}
.listing-detail .listing-content .trans-accordion .accordion-item { padding: 15px 0;}

.listing-detail .listing-content .faq-accordion .accordion-item { background-color: #FBFBFB; margin-bottom: 20px; padding: 20px 15px; border-radius: 10px; border: 1.5px solid var(--cloud-gray); }
/* .listing-detail .listing-content .faq-accordion .accordion-item button { background-color: transparent;} */
.listing-archive select.sorting-select {border-radius: 5px;border: 1px solid var(--cloud-gray);height: 40px;padding: 0px 8px 0px 13px;color: #AFB2B8;}
.listing-archive select.sorting-select:focus-visible {outline: 0;}
 .sorting-wrapper .view-change:focus-visible {outline: 0;}
/* Service Detail End */


/* testimonial-slider */
.testimonialSwiper {border-radius: 22px;background: var(--midnight-navy);}




/*Service-Detail Security-Badge */
.security-badge-inner {background: var(--white);padding: 6px 16px;border-radius: 999px;}
.security-badge {display: inline-block;padding: 2px;background: linear-gradient(90deg, #f2994a, #a100ff);border-radius: 999px;top: 9px;left: 3px;}

.summit-blank-sec {height: 16vh;}
.simmit-image {margin-top: -4em;}
.profile-inner {padding: 0 25px 19px;}
.profile-parent {margin-top: -7em;border-radius: 8px;border: 1px solid var(--cloud-gray);background: #FFF;box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.25);}
.summit-icon i {border-radius: 8px;border: 1px solid var(--cloud-gray);background: #FBFBFB;padding: 8px;font-size: 20px;}
.summit-icon .fa-brands.fa-instagram {background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;background-clip: text;}
.summit-icon .fa-brands.fa-facebook {color: #0866FF;}
.summit-icon img {padding: 4px;border-radius: 8px;border: 1px solid var(--cloud-gray);background: #FBFBFB;width: 36px;height: 36px;}
.summit-icon .fa-brands.fa-tiktok {background: linear-gradient(45deg, #FF004F, #00F2EA);-webkit-background-clip: text;-webkit-text-fill-color: transparent;background-clip: text;}
.button-box .btn-gridient {border-radius: 8px;width: 100%;}
.button-box .learn-more-btn {width: 100%;padding: 10px 18px;border-radius: 8px;border: 1px solid var(--cloud-gray);}
.acme-vadu {background-color: var(--ghost-white);border-radius: 5px;padding: 14px 16px;}
.acma-text-listing {border-bottom: 1px solid var(--cloud-gray);padding: 0 0 17px 0;}


/* Home-page-Banner */
.hero-section:before { content: ''; position: absolute;top: -21%;left: 40%; width: 398px;height: 391px;background-size: cover;background-position: center;background-image: url(../media/images/abstract-design-banner.png);}
.hero-section h1.section-title {width: 90%;margin: auto;position: relative;z-index: 1; }
.hero-section{ background: url(../media/images/hero-banner-bg.png) no-repeat center center; background-size: cover; padding-block:10em ;}

.location-section {padding: 0 20px;}
.search-icon {width: 20px;height: 20px;color: #AFB2B8; }
.search-wrapper {width: 100%;max-width: 905px;margin: auto;position: relative; z-index: 1;}
.search-container {border-radius: 50px;border: 1px solid var(--cloud-gray);background: var(--white);box-shadow: 0px 6px 6px 0px rgba(0, 0, 0, 0.04);padding: 8px 12px 8px 20px;gap: 245.652px;}
.custom-grid-sec:before {content: '';position: absolute;top: 33%;right: 0;width: 100%;height: 192px;background-image: url(../media/images/grid-vector.png);background-size: cover;background-position: center;}

.search-btn-icon {width: 16px;height: 16px;}
.search-btn {display: flex;align-items: center;gap: 6px;min-width: fit-content;padding: 13px 22px;}

.stats-container {border-radius: 24px;border: 1px solid var(--white);}
.verified-users-inner {padding: 8px 12px 8px 8px;border-radius: 100px;border: 1px solid var(--white);background: rgba(255, 255, 255, 0.40);box-shadow: 0px 2px 3px 0px rgba(183, 183, 183, 0.25);backdrop-filter: blur(2.5px);position: absolute;top: -8px;}
.verified-users-inner:before {content: '';position: absolute;background-image: url(../media/images/stroke-left.svg);width: 39px;height: 25px;left: -16%;}
.verified-users-inner:after {content: '';position: absolute;background-image: url(../media/images/stroke-right.svg);width: 39px;height: 25px;right: -15%;}

.avatar:first-child {margin-left: 0;background: linear-gradient(45deg, #f59e0b, #f97316);}

.avatar:nth-child(2) {background: linear-gradient(45deg, #10b981, #059669);}

.stats-grid { display: grid; grid-template-columns: repeat(3, 1fr);gap: 40px; text-align: center; padding: 40px 20px 20px 20px;}
.user-avatars {display: flex;margin-right: 8px;}

.stat-item {border-radius: 20px;border: 1px solid var(--cloud-gray);background: linear-gradient(180deg, rgba(242, 231, 254, 0.50) -35.33%, rgba(238, 231, 254, 0.50) 46.23%, rgba(255, 255, 255, 0.00) 100%), rgba(255, 255, 255, 0.50);padding: 40px 24px;}


.stats-grid:before {content: '';background-image: url(../media/images/hero-line-left.png);bottom: 91.4%;}
.stats-grid:after {content: '';background-image: url(../media/images/hero-line-right.png);bottom: 91.4%;right: 3%;}
.stats-grid:before, .stats-grid:after{background-size: cover;background-position: center;position: absolute;width: 100px;height: 502px;}
.avatar { width: 32px; height: 32px; border-radius: 50%; border: 2px solid white; margin-left: -8px; background: linear-gradient(45deg, #8b5cf6, #a855f7); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;font-size: 12px;}
.stat-item {animation: fadeInUp 0.6s ease forwards;}
.stat-item:nth-child(2) {animation-delay: 0.2s;}
.stat-item:nth-child(3) {animation-delay: 0.4s;}


@media (max-width: 768px) {.stats-grid {grid-template-columns: 1fr;gap: 30px;}.stats-container {padding: 40px 20px;}.stat-number {font-size: 36px;}}

.search-input { border: none; outline: none; background: transparent; font-size: 16px; color: #374151; flex: 1; padding: 16px 0; font-weight: 400; }
.search-input::placeholder { color: #9ca3af; font-weight: 400; }
.location-icon { width: 18px; height: 18px; color: #9ca3af; flex-shrink: 0; }
.vertical-divider { width: 1px; height: 24px; background: #e5e7eb; margin: 0 8px; }
.location-input { border: none; outline: none; background: transparent; font-size: 16px; color: #374151; width: 100%; padding: 16px 0; font-weight: 400; }
.location-input::placeholder { color: #9ca3af; font-weight: 400; }
.search-btn.loading { background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%); cursor: not-allowed; }  
.search-btn.loading .search-btn-icon { animation: spin 1s linear infinite; }     

@keyframes spin {from { transform: rotate(0deg); }to { transform: rotate(360deg); }}
@keyframes shake {0%, 100% { transform: translateX(0); }25% { transform: translateX(-4px); }75% { transform: translateX(4px); }}

.shake {animation: shake 0.3s ease-in-out; }

        /* Focus states */
.search-input:focus + .search-icon, .location-input:focus + .location-icon { color: #f97316; }     
.search-input:focus, .location-input:focus { color: #111827; }    
        /* Responsive */
@media (max-width: 640px) {
.search-container { flex-direction: column; border-radius: 24px; padding: 12px; gap: 8px; }       
.search-input-section, .location-section { width: 100%; background: #f9fafb; border-radius: 16px; padding: 16px 20px; }  
.vertical-divider {display: none;}
.search-btn { width: 100%;justify-content: center;border-radius: 16px;padding: 16px 24px;}
.location-section {min-width: unset;}
}



/* service-detail-modal */
#projectModal .modal-dialog {max-width: 600px;}
.pm-boder-right {border-right: 2px solid var(--cloud-gray);padding-right: 6em;}
.project-modal-parent {border-radius: 10px;background: var(--white);width: 600px;height: 658px;}
.project-modal-header {border-radius: 7px;border: 1px solid var(--cloud-gray);background: #FBFBFB;}
.custom-modal-header {padding: 15px 15px 0 15px;border-bottom: 0;}
 
/* contractor-list-card */
.contrator-list-card-inner img {border-radius: 4px;} 
.contrator-list-card {border-radius: 8px;border: 1px solid var(--cloud-gray);background: var(--white);box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.25);padding: 13px;}
.contractor-wishlist .wishlist-btn {left: 6px;}
.contractor-wishlist .wishlist-btn i { width: 31px;height: 31px;line-height: 1.6;color: var(--dark-black) !important;}
.contrator-list-card-inner {border-right: 2px solid #EBECED;width: 75%;}




/* about-us */
.prioritize { border-radius: 12px; border: 2px solid rgba(236, 155, 110, 0.20); background: radial-gradient(151.92% 127.02% at 15.32% 21.04%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.04) 77.08%, rgba(255, 255, 255, 0.00) 100%); backdrop-filter: blur(19px); padding: 26px 24px; width: 75%; margin: auto;}
.check-list-items:before { content: ''; position: absolute; width: 544px; height: 655px; background-size: cover; background-position: center; background-repeat: no-repeat; background-image: url(../media/images/stone-color.png); top: -12%; right: -20%; transform: rotate(18deg); }


.custom-gradient {background: linear-gradient(90deg, #0f0022, #5f3a78, #f9a84b);padding: 3rem 2rem;border-radius: 20px;position: relative;z-index: 2;}
.info-box {border-radius: 12px;border: 2px solid rgba(236, 155, 110, 0.20);background-blend-mode: overlay, normal;box-shadow: 2px 16px 19px 0px rgba(0, 0, 0, 0.09);backdrop-filter: blur(40px);}


.about-accordion .nav-tabs{border:0px; gap: 10px;}
.about-accordion .nav-tabs .nav-link.active {color: var(--white);background: linear-gradient(90deg, #EC9B6E 48.36%, #9D6FD9 100%), #9B6CD6;}
.about-accordion .nav-tabs .nav-link {border-radius: 20px;color: var(--dark-black);border: 1px solid var(--cloud-gray);}
.about-accordion .accordion-button .collapsed{background-color: var(--dusk-blue);}
.about-accordion .accordion-button:focus{outline: 0;box-shadow: none;}
.about-accordion .accordion-button{background-color: var(--light-grey);}
.about-accordion .accordion-button:not(.collapsed){background-color: var(--light-grey);}
.about-accordion .accordion-item{border:0;}
.about-accordion .accordion-img img, svg { vertical-align: middle; width: 100%; height: 100%; }

.about-accordion .accordion-header {border-radius: 5px;background: var(--pale-silver);}
.about-accordion .accordion-button:not(.collapsed) {color: var(--midnight-slate);}
.stats-main {border-left: dashed #9B6CD6;}



 
   
.gallery-container { max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 160px 160px 70px 160px; gap: 10px; }.gallery-item img {border-radius: 6px; width: 100%;height: 100%;object-fit: cover;display: block;}
.item-1 {grid-column: 1 / -1;grid-row: 1;}
.item-2 { grid-column: 1;grid-row: 2;height: 160px;}
.item-3 {grid-column: 2;grid-row: 2 / 4;}
.item-4 { grid-column: 1;grid-row: 3 / 5;}
.item-5 {grid-column: 2;grid-row: 4;}

@media (max-width: 768px) {
.gallery-container {grid-template-columns: 1fr;grid-template-rows: repeat(5, 220px);}
.item-1 { grid-column: 1; grid-row: 1; }
.item-2 { grid-column: 1; grid-row: 2; }
.item-3 { grid-column: 1; grid-row: 3; }
.item-4 { grid-column: 1; grid-row: 4; }
.item-5 { grid-column: 1; grid-row: 5; }
}

.contact-form {width: 500px; margin: auto;}
.contact-form button {border-radius: 8px;}
textarea.form-control:focus {border-radius: 8px;border: 1px solid #EBECED;background: var(--white);}


/* terms-and-condition */
.terms-sidebar-nav li a{padding: 12px 10px; }
.terms-sidebar-nav li {list-style: none;padding-bottom: 13px;}
.terms-sidebar-nav li a.active {border-radius: 6px;background: var(--pale-silver);display: flex;padding: 12px 10px;align-items: center;color:var(--purple);font-weight: 600;}


/* customer-header */
.customer-dropdown {padding: 5px 10px 5px 5px; background: linear-gradient(90deg, rgb(236, 155, 110) 48.36%, rgb(157, 111, 217) 100%);border-radius: 50px;}
.customer-dropdown .dropdown-menu.show {right: 0;}
.customer-dropdown img {border-radius: 100px;border: 1px solid #FFF;}
.customer-notification a i {font-size: 18px;color: #656B76;}
.customer-notification a i:hover {background: linear-gradient(90deg, #EC9B6E 48.36%, #9D6FD9 100%);background-clip: text;color: transparent;}
.customer-notification {border-radius: 50px;border: 1px solid rgba(0, 0, 0, 0.06);padding: 10px 18px;}
/* Custom dropdown header with image */
.customer-dropdown .dropdown-menu {min-width: 240px;}
.customer-dropdown .dropdown-menu.show { right: 0; border-radius: 6px; border: 1px solid #FFF;box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.09);}
.customer-dropdown .dropdown-menu li:first-child {border-bottom: 2px solid #E9EAEB;}
.customer-dropdown ul li i {color: var(--dark-black);font-size: 14px;}
.customer-dropdown ul li a:hover {background-color: transparent;}
.customer-dropdown .dropdown-menu li:last-child {border-top: 2px solid #E9EAEB;}
.customer-dropdown .dropdown-menu li:last-child i {color: #FF4242;}
.customer-dropdown .dropdown-menu li:last-child a {color: #FF4242;}