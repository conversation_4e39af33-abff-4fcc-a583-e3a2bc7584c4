<div class="container">
    <header class="vadu-header">
        <nav class="navbar navbar-expand-lg navbar-light p-0">
            <div class="container px-5 me-3 ms-3">
                <!-- Logo -->
                <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
                    <div class="d-flex align-items-center">
                        <img src="<?php echo e(asset('website/assets/media/images/vadu-website-logo.svg')); ?>" alt="VADU Logo"
                            width="127" height="40">
                    </div>
                </a>

                <!-- Hamburger button for mobile -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Navigation items -->
                <div class="collapse navbar-collapse justify-content-end gap-15" id="navbarNav">
                    <?php if(auth()->guard()->check()): ?>
                    <?php if(auth()->user()->hasRole('customer')): ?>
                    <!-- Customer Header Actions -->
                    <div class="d-flex align-items-center gap-3">
                        <!-- Notifications -->
                        <div class="header-icon-wrapper">
                            <button class="header-icon-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                                <li class="dropdown-header">Notifications</li>
                                <li><a class="dropdown-item" href="#">New message from contractor</a></li>
                                <li><a class="dropdown-item" href="#">Project update available</a></li>
                                <li><a class="dropdown-item" href="#">Quote received</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#">View all notifications</a></li>
                            </ul>
                        </div>

                        <!-- Messages -->
                        <div class="header-icon-wrapper">
                            <a href="<?php echo e(route('chat')); ?>" class="header-icon-btn">
                                <i class="fas fa-envelope"></i>
                                <span class="message-badge">2</span>
                            </a>
                        </div>

                        <!-- Favorites/Wishlist -->
                        <div class="header-icon-wrapper">
                            <button class="header-icon-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-heart"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end wishlist-dropdown">
                                <li class="dropdown-header">Saved Contractors</li>
                                <li><a class="dropdown-item" href="#">ABC Construction</a></li>
                                <li><a class="dropdown-item" href="#">Elite Builders</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#">View all saved</a></li>
                            </ul>
                        </div>

                        <!-- User Profile -->
                        <div class="dropdown">
                            <button class="user-profile-btn d-flex align-items-center" type="button"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar-header">
                                    <img src="<?php echo e(auth()->user()->avatar ?? asset('website/assets/media/images/default-avatar.png')); ?>"
                                         alt="<?php echo e(auth()->user()->name); ?>"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="user-avatar-fallback">
                                        <?php echo e(strtoupper(substr(auth()->user()->name, 0, 2))); ?>

                                    </div>
                                </div>
                                <i class="fas fa-chevron-down ms-2"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                                <li class="dropdown-header">
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar-small me-2">
                                            <?php echo e(strtoupper(substr(auth()->user()->name, 0, 2))); ?>

                                        </div>
                                        <div>
                                            <div class="fw-600"><?php echo e(auth()->user()->name); ?></div>
                                            <small class="text-muted">Customer</small>
                                        </div>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>My Profile</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-project-diagram me-2"></i>My Projects</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('chat')); ?>"><i class="fas fa-comments me-2"></i>Messages</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-heart me-2"></i>Saved Items</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(url('/logout')); ?>"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </div>
                    </div>
                    <?php else: ?>
                    <!-- Default/Contractor Navigation -->
                    <ul class="navbar-nav gap-15">
                        <li class="nav-item">
                            <a class="nav-link px-3" href="<?php echo e(url('contractor')); ?>">Contractors</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-3" href="<?php echo e(url('title-escrow')); ?>">Title & Escrow</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-3" href="<?php echo e(url('mortgage-lenders')); ?>">Mortgage Lenders</a>
                        </li>
                    </ul>

                    <!-- Contractor Profile & Actions -->
                    <div class="d-flex align-items-center mt-3 mt-lg-0 gap-8">
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar-small me-2">
                                    <?php echo e(strtoupper(substr(auth()->user()->name, 0, 2))); ?>

                                </div>
                                <?php echo e(auth()->user()->name); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">Dashboard</a></li>
                                <li><a class="dropdown-item" href="#">My Services</a></li>
                                <li><a class="dropdown-item" href="#">Profile Settings</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="<?php echo e(url('/logout')); ?>">Logout</a></li>
                            </ul>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <!-- Guest Navigation -->
                    <ul class="navbar-nav gap-15">
                        <li class="nav-item">
                            <a class="nav-link px-3" href="<?php echo e(url('contractor')); ?>">Contractors</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-3" href="<?php echo e(url('title-escrow')); ?>">Title & Escrow</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-3" href="<?php echo e(url('mortgage-lenders')); ?>">Mortgage Lenders</a>
                        </li>
                    </ul>

                    <!-- Auth buttons -->
                    <div class="d-flex align-items-center mt-3 mt-lg-0 gap-8">
                        <a href="<?php echo e(route('login')); ?>"
                            class="btn-login fs-16 fw-500 border-dusk-blue text-midnight-navy border-radius">Login</a>
                        <a href="#" class="btn-gridient fs-16 fw-500">Become a Contractor</a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
    </header>
</div><?php /**PATH D:\Github\vadu\resources\views/website/templates/header.blade.php ENDPATH**/ ?>