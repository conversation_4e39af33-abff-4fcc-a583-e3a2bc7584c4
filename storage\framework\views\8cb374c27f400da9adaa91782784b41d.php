<?php $__env->startSection('content'); ?>

<section class="">
    <div class="container">
        <div class="row">
            <div class="col-12 mb-10">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="page_title">
                        <h1>Service Categories</h1>
                    </div>
                    <a href="<?php echo e(route('admin.service-categories.create')); ?>" class="btn_purple"> <span><i class="fa-solid fa-plus text-white me-2"></i></span>Add Services</a>
                </div>
            </div>
        </div>

        <div class="row py-4">
            <div class="col-md-12">
                <div class="table_container">
                    <div class="table_header">
                        <div class="search_container m-0">
                            <i class="fas fa-search search_icon"></i>
                            <input type="text" id="customSearch" placeholder="Search..." class="form-control">
                        </div>
                        <div class="table_filter d-flex align-items-center justify-content-end gap-2">
                            <button type="button" class="btn_white"> <span><img src="<?php echo e(asset('website')); ?>/assets/images/icon-filter.svg"></span>Filters</button>
                        </div>
                    </div>


                    <table id="myTable" class="display table table-borderless">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll"></th>
                                <th>Service Tags</th>
                                <th>Professional</th>
                                <th>Property Type</th>
                                <th>Architectural</th>
                                <th>Status</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php for($i=0; $i<=10; $i++): ?>

                                <tr>
                                <td><input type="checkbox" class="rowCheckbox"></td>
                                <td>
                                    <div class="profile_card">
                                        <img class="rounded-square" src="http://127.0.0.1:8000/website/assets/media/avatars/300-3.jpg" alt="user avatar">
                                        <div class="profile_info">
                                            <div class="name">Bathroom Remodeling</div>
                                        </div>
                                    </div>
                                </td>
                                <td>(302) 555-0107</td>
                                <td>Flooring Installation</td>
                                <td><span class="date_text">Jan 5, 2025</span></td>
                                <td>
                                    <span class="status_badge status_active">
                                        <span class="status_dot"></span>
                                        Active
                                    </span>
                                </td>
                                <td>
                                    <div class="dropdown">
                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                <li>
                                                    <a href="<?php echo e(route('admin.service-categories.create')); ?>" class="dropdown-item complete fs-14 regular">
                                                        Add
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo e(route('admin.service-categories.create')); ?>" class="dropdown-item cancel fs-14 regular">
                                                        Edit
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                </td>
                                </tr>

                                <tr>
                                    <td><input type="checkbox" class="rowCheckbox"></td>
                                    <td>
                                        <div class="profile_card">
                                            <img class="rounded-square" src="http://127.0.0.1:8000/website/assets/media/avatars/300-3.jpg" alt="user avatar">
                                            <div class="profile_info">
                                                <div class="name">Bathroom Remodeling</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>(302) 555-0107</td>
                                    <td>Painting</td>
                                    <td><span class="date_text">Jan 5, 2025</span></td>
                                    <td>
                                        <span class="status_badge status_inactive">
                                            <span class="status_dot"></span>
                                            Inactive
                                        </span>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                <li>
                                                    <a href="<?php echo e(route('admin.service-categories.create')); ?>" class="dropdown-item complete fs-14 regular">
                                                        Add
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="<?php echo e(route('admin.service-categories.create')); ?>" class="dropdown-item cancel fs-14 regular">
                                                        Edit
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endfor; ?>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    $(document).ready(function() {
        const startOfMonth = moment().startOf('month');
        const currentDate = moment();

        $("#date-range").daterangepicker({
            startDate: startOfMonth,
            endDate: currentDate,
            locale: {
                format: 'MMM D, YYYY' // This changes the display format
            }
        });

        $("#date-range").on('apply.daterangepicker', function(e, picker) {
            e.preventDefault();
            const obj = {
                "key": dates.length + 1,
                "start": picker.startDate.format('MMM D, YYYY'), // Changed format here
                "end": picker.endDate.format('MMM D, YYYY') // Changed format here
            };
            dates.push(obj);

            // If you want to display the range with an en dash (–) between dates
            const displayRange = picker.startDate.format('MMM D, YYYY') + ' – ' + picker.endDate.format('MMM D, YYYY');
            $("#date-range").val(displayRange);
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views\dashboard\admin\service-categories\index.blade.php ENDPATH**/ ?>