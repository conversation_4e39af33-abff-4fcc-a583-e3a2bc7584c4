<?php

use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ThemeController;


use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\WebsiteController;
use Illuminate\Support\Facades\Artisan;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/test', function () {
    $permissions = Permission::pluck('id', 'id')->all();
    return $permissions; // Ensure you are returning the variable here
});

// Cache Clear Route
Route::get('/clear-all', function () {
    //$exitCodeConfig = Artisan::call('storage:link');
    $exitCodeRoute = Artisan::call('route:clear');
    $exitCodeCache = Artisan::call('cache:clear');
    $exitCodeUpdate = Artisan::call('optimize:clear');
    $exitCodeView = Artisan::call('view:clear');
    $exitConfigCache = Artisan::call('config:clear');
    $exitConfigviewclear = Artisan::call('view:cache');
    return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Back to Home</a></h4></div>';
});
// Route::get('/', function () {
//     return view('website.index');
// });
Route::get('/logout', function () {
    Auth::logout();
    return redirect('/'); // Redirect the user after logout
});

// Route::get('/', function () {
//     if (auth()->user()) {
//         return redirect('dashboard');
//     } else {
//         return view('auth.login');
//     }
// })->middleware('auth');

Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');

Route::get('permissions', [DashboardController::class, 'permissions'])->name('permissions')->middleware('auth');
Auth::routes();
Route::resource("settings", "\App\Http\Controllers\SettingsController")->middleware("auth");
// Route::fallback(function(){
// route(404);
// });

Route::group(['middleware' => ['auth']], function () {
    Route::resource('roles', RoleController::class);
    //Route::resource('roles/{id?}', RoleController::class)->name('roles.edit');
    Route::resource('users', UserController::class);
});


// Website Routes
Route::get('/', action: [WebsiteController::class, 'index'])->name('home');
Route::get('about-us', action: [WebsiteController::class, 'aboutUs'])->name('about-us');
Route::get('contractor', action: [WebsiteController::class, 'contractor'])->name('contractor');
Route::get('title-escrow', action: [WebsiteController::class, 'titleEscrow'])->name('title-escrow');
Route::get('mortgage-lenders', action: [WebsiteController::class, 'mortgageLenders'])->name('mortgage-lenders');
Route::get('service-detail', action: [WebsiteController::class, 'serviceDetail'])->name('service-detail');
Route::get('contact-us', action: [WebsiteController::class, 'contactUs'])->name('contact-us');
Route::get('terms-condition', action: [WebsiteController::class, 'termsCondition'])->name('terms-condition');
Route::get('chat', action: [WebsiteController::class, 'chat'])->name('chat');


// Dashboard Routes
Route::middleware(['auth'])->group(function(){
    Route::get('/home', [DashboardController::class, 'dashboard'])->name('dashboard');
    Route::get('/admin/contractors', [DashboardController::class, 'contractors']) ->name('admin.contractors');
    Route::get('/admin/contractors/profile', [DashboardController::class, 'contractorProfile']) ->name('admin.contractors.profile');
    Route::get('/admin/title-escrow', [DashboardController::class, 'titleEscrow']) ->name('admin.title-escrow');
    Route::get('/admin/title-escrow/create', [DashboardController::class, 'titleEscrowCreate']) ->name('admin.title-escrow.create');
    Route::get('/admin/title-escrow/edit', [DashboardController::class, 'titleEscrowEdit']) ->name('admin.title-escrow.edit');
    Route::get('/admin/mortgage-lenders', [DashboardController::class, 'mortgageLenders']) ->name('admin.mortgage-lenders');
    Route::get('/admin/mortgage-lenders/create', [DashboardController::class, 'mortgageLendersCreate']) ->name('admin.mortgage-lenders.create');
    Route::get('/admin/mortgage-lenders/edit', [DashboardController::class, 'mortgageLendersEdit']) ->name('admin.mortgage-lenders.edit');
    Route::get('/admin/service-categories', [DashboardController::class, 'serviceCategories']) ->name('admin.service-categories');
    Route::get('/admin/service-categories/create', [DashboardController::class, 'serviceCategoriesCreate']) ->name('admin.service-categories.create');
    Route::get('/admin/subscription', [DashboardController::class, 'subscription']) ->name('admin.subscription');
    Route::get('/profile_settings', [DashboardController::class, 'profileSettings']) ->name('profile_settings');
});

//Stepper form route
Route::get('professional_account', [DashboardController::class, 'professional_account'])->name('professional_account_stepper');
