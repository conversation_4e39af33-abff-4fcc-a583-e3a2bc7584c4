<?php

use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\WebsiteController;
use Illuminate\Support\Facades\Artisan;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AuthController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/test', function () {
    $permissions = Permission::pluck('id', 'id')->all();
    return $permissions; // Ensure you are returning the variable here
});

// Cache Clear Route
Route::get('/clear-all', function () {
    Artisan::call('route:clear');
    Artisan::call('cache:clear');
    Artisan::call('optimize:clear');
    Artisan::call('view:clear');
    Artisan::call('config:clear');
    Artisan::call('view:cache');
    return back()->with(['type' => 'success', 'title' => 'Done', 'message' => 'Cache and Config and permission cache are cleared.']);
});

// Signup Routes
Route::post('/signup', [AuthController::class, 'signup'])->name('signup');
Route::get('/register/{user_type}', [AuthController::class, 'registerUserType'])->name('register.user_type')->where('user_type', 'customer|professional');


Route::get('/logout', function () {
    Auth::logout();
    return redirect('/'); // Redirect the user after logout
});

Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');

Route::get('permissions', [DashboardController::class, 'permissions'])->name('permissions')->middleware('auth');
Auth::routes();
Route::resource("settings", "\App\Http\Controllers\SettingsController")->middleware("auth");


Route::group(['middleware' => ['auth']], function () {
    Route::resource('roles', RoleController::class);
    //Route::resource('roles/{id?}', RoleController::class)->name('roles.edit');
    Route::resource('users', UserController::class);
});


// Website Routes
Route::get('/', action: [WebsiteController::class, 'index'])->name('home');
Route::get('about-us', action: [WebsiteController::class, 'aboutUs'])->name('about-us');
Route::get('contractor', action: [WebsiteController::class, 'contractor'])->name('contractor');
Route::get('title-escrow', action: [WebsiteController::class, 'titleEscrow'])->name('title-escrow');
Route::get('mortgage-lenders', action: [WebsiteController::class, 'mortgageLenders'])->name('mortgage-lenders');
Route::get('our-services', action: [WebsiteController::class, 'services'])->name('our-services');
Route::get('service-detail', action: [WebsiteController::class, 'serviceDetail'])->name('service-detail');
Route::get('contact-us', action: [WebsiteController::class, 'contactUs'])->name('contact-us');
Route::get('terms-condition', action: [WebsiteController::class, 'termsCondition'])->name('terms-condition');
Route::get('privacy-policy', action: [WebsiteController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('chat', action: [WebsiteController::class, 'chat'])->name('chat');
Route::get('notification', action: [WebsiteController::class, 'notification'])->name('notification');
Route::get('favorite-professional', action: [WebsiteController::class, 'favoriteProfessional'])->name('favorite-professional')->middleware(['auth']);
Route::get('profile-setting', action: [WebsiteController::class, 'profileSetting'])->name('profile-setting')->middleware(['auth']);
Route::get('summit-engineering', action: [WebsiteController::class, 'summitEngineering'])->name('summit-engineering');
Route::get('coming-soon', action: [WebsiteController::class, 'comingSoon'])->name('coming-soon');



// Dashboard Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/home', [DashboardController::class, 'dashboard'])->name('dashboard');
    // admin pages
    Route::get('/admin/contractors', [DashboardController::class, 'contractors'])->name('admin.contractors');
    Route::get('/admin/contractors/profile', [DashboardController::class, 'contractorProfile'])->name('admin.contractors.profile');
    Route::get('/admin/contractors/profile-detail', [DashboardController::class, 'contractorProfileDetail'])->name('admin.contractors.profile-detail');
    Route::get('/admin/title-escrow', [DashboardController::class, 'titleEscrow'])->name('admin.title-escrow');
    Route::get('/admin/title-escrow/create', [DashboardController::class, 'titleEscrowCreate'])->name('admin.title-escrow.create');
    Route::get('/admin/title-escrow/edit', [DashboardController::class, 'titleEscrowEdit'])->name('admin.title-escrow.edit');
    Route::get('/admin/mortgage-lenders', [DashboardController::class, 'mortgageLenders'])->name('admin.mortgage-lenders');
    Route::get('/admin/mortgage-lenders/create', [DashboardController::class, 'mortgageLendersCreate'])->name('admin.mortgage-lenders.create');
    Route::get('/admin/mortgage-lenders/edit', [DashboardController::class, 'mortgageLendersEdit'])->name('admin.mortgage-lenders.edit');
    Route::get('/admin/service-categories', [DashboardController::class, 'serviceCategories'])->name('admin.service-categories');
    Route::get('/admin/service-categories/create', [DashboardController::class, 'serviceCategoriesCreate'])->name('admin.service-categories.create');
    Route::get('/admin/subscription', [DashboardController::class, 'subscription'])->name('admin.subscription');
    Route::get('/profile_settings', [DashboardController::class, 'profileSettings'])->name('profile_settings');
    Route::get('/notifications', [DashboardController::class, 'notifications'])->name('notifications');
    Route::get('/services', [DashboardController::class, 'serviceIndex'])->name('services.index');

    // professional pages
    Route::get('/professional/projects', [DashboardController::class, 'projects'])->name('professional.projects');
    Route::get('/professional/projects/create', [DashboardController::class, 'projectsCreate'])->name('professional.projects.create');
    Route::get('/professional/quote-request', [DashboardController::class, 'quoteRequest'])->name('professional.quote-request');
});



