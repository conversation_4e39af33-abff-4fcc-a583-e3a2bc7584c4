<?php $__env->startSection('content'); ?>

<form action="" method="POST" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <section class="">
        <div class="container">
            <div class="row">
                <div class="col-12 mb-10">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="page_title">
                            <h1>Add Service Categories</h1>
                            <p>Service Categories <span><i class="fa-solid fa-angle-right"></i></span> Add Service Categories</p>
                        </div>
                        <button type="submit" class="btn_purple">Add</button>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-3">
                    <input name="file1" type="file" class="dropify"
                        data-height="126"
                        data-max-file-size="2M"
                        data-allowed-file-extensions="png jpg jpeg"
                        data-max-width="800"
                        data-max-height="400" />
                </div>
            </div>
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="category_tags" class="form-label">Category Tag</label>
                    <select name="category_tags[]" class="form-control category_tags <?php $__errorArgs = ['category_tags'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        id="category_tags" multiple="multiple">
                        <option>Tag1</option>
                        <option>Tag2</option>
                        <option>Tag3</option>
                        <option>Tag4</option>
                    </select>
                    <?php $__errorArgs = ['tags'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="col-md-6">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" aria-label="Default select example">
                        <option selected>Select category</option>
                        <option value="1">One</option>
                        <option value="2">Two</option>
                        <option value="3">Three</option>
                    </select>
                    <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="col-6">
                    <label for="property" class="form-label">Property</label>
                    <select class="form-select" aria-label="Default select example">
                        <option selected>Select property</option>
                        <option value="1">One</option>
                        <option value="2">Two</option>
                        <option value="3">Three</option>
                    </select>
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="col-md-6">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" aria-label="Default select example">
                        <option selected>Select category</option>
                        <option value="1">One</option>
                        <option value="2">Two</option>
                        <option value="3">Three</option>
                    </select>
                    <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

            </div>
        </div>
    </section>
</form>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    $(document).ready(function() {
        // Dropify initialization
        $('.dropify').dropify({
            messages: {
                'default': 'Click to upload or drag and drop',
                'replace': 'Click to upload or drag and drop',
                'remove': 'Remove',
                'error': 'Ooops, something wrong happened.'
            },
            error: {
                'fileSize': 'The file size is too big (max 2MB).',
                'imageFormat': 'The image format is not allowed (PNG, JPG only).',
                'imageWidth': 'The image width must be under 800px.',
                'imageHeight': 'The image height must be under 400px.'
            }
        });

        // Select2 initialization for tags
        $('.category_tags').select2({
            theme: 'bootstrap-5',
            tags: true,
            tokenSeparators: [',', ' '],
            placeholder: 'Select Tags...',
            allowClear: true,
            createTag: function(params) {
                var term = $.trim(params.term);
                if (term === '') {
                    return null;
                }
                return {
                    id: term,
                    text: term,
                    newTag: true
                };
            },
            templateResult: function(data) {
                var $result = $("<span></span>");
                $result.text(data.text);
                if (data.newTag) {
                    $result.append(" <em>(new)</em>");
                }
                return $result;
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views\dashboard\admin\service-categories\create.blade.php ENDPATH**/ ?>