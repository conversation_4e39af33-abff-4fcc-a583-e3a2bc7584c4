@extends('theme.layout.master')

@push('css')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.17/css/intlTelInput.css" />
@endpush

@section('content')
<section class="settings">
    <div class="container">
        <div class="row">
            <div class="col-12 mb-10">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="page_title">
                        <h1>Settings</h1>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 mb-4">
                <!-- Tabs -->
                <ul class="nav nav-tabs mt-4" id="settingsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">Profile</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password" type="button" role="tab">Password</button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content pt-4" id="settingsTabContent">
                    <!-- Profile Tab -->
                    <div class="tab-pane fade show active" id="profile" role="tabpanel">
                        <form>
                            <div class="row">
                                <div class="col-12 mb-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="page_title">
                                            @if(auth()->user()->hasRole('admin'))
                                            <h5>Personal info</h5>
                                            @endif
                                            @if(auth()->user()->hasRole('professional'))
                                            <h5>Business info</h5>
                                            @endif
                                            <p>Update your photo and personal details here.</p>
                                        </div>
                                        <div class="d-flex align-items-center gap-3">
                                            <button type="reset" class="btn_white">Cancel</button>
                                            <button type="submit" class="btn_purple">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="name" class="col-sm-2 col-form-label">Name</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" id="name">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="email" class="col-sm-2 col-form-label">Email</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <div class="input-group-text bg-white"><i class="fa-regular fa-envelope"></i></div>
                                        <input type="email" name="email" class="form-control" id="email" placeholder="email">
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="phone_number" class="col-sm-2 col-form-label">Phone Number</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <div class="input-group-text bg-white"><i class="fa-solid fa-phone"></i></div>
                                        <input type="tel" class="form-control" name="phone_number" id="phone_number">
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="address" class="col-sm-2 col-form-label">Address</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <div class="input-group-text bg-white"><i class="fa-solid fa-location-dot"></i></div>
                                        <input type="text" class="form-control" name="address" id="address">
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="photo" class="col-sm-2 col-form-label">Your photo</label>
                                <div class="col-sm-8">
                                    <input name="photo" type="file" class="dropify"
                                        data-height="126"
                                        data-max-file-size="2M"
                                        data-allowed-file-extensions="png jpg jpeg"
                                        data-max-width="800"
                                        data-max-height="400" />
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="country" class="col-sm-2 col-form-label">Country</label>
                                <div class="col-sm-8">
                                    <div class="country-select-wrapper">
                                        <div class="flag-icon" id="selected-flag">
                                            <img src="https://flagcdn.com/w40/us.png" alt="Flag">
                                        </div>
                                        <select id="country_select" name="country" class="country-select form-select">
                                            <option data-flag="us" value="United States" selected>United States</option>
                                            <option data-flag="gb" value="United Kingdom">United Kingdom</option>
                                            <option data-flag="in" value="India">India</option>
                                            <option data-flag="pk" value="Pakistan">Pakistan</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            @if(auth()->user()->hasRole('professional'))
                            <div class="row">
                                <div class="col-12 my-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="page_title">
                                            <h5>Social media</h5>
                                            <p>Update your social media here.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="facebook" class="col-sm-2 col-form-label">Facebook</label>
                                <div class="col-sm-8">
                                    <input type="url" class="form-control" name="facebook" id="facebook">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="twitter" class="col-sm-2 col-form-label">Twitter</label>
                                <div class="col-sm-8">
                                    <input type="url" class="form-control" name="twitter" id="twitter">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="instagram" class="col-sm-2 col-form-label">Instagram</label>
                                <div class="col-sm-8">
                                    <input type="url" class="form-control" name="instagram" id="instagram">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="x_com" class="col-sm-2 col-form-label">X.com</label>
                                <div class="col-sm-8">
                                    <input type="url" class="form-control" name="x_com" id="x_com">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="tiktok" class="col-sm-2 col-form-label">Tiktok</label>
                                <div class="col-sm-8">
                                    <input type="url" class="form-control" name="tiktok" id="tiktok">
                                </div>
                            </div>
                            @php
                            $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                            @endphp

                            <div class="row">
                                <div class="col-12 my-10">
                                    <h5>Business hour</h5>
                                    <p>Update your business hour here.</p>
                                </div>
                            </div>

                            @foreach($days as $day)
                            <div class="row align-items-center mb-3 business-row" data-day="{{ strtolower($day) }}">
                                <div class="col-sm-2">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" id="{{ strtolower($day) }}_active"
                                            name="business_hours[{{ $day }}][active]"
                                            {{ $day !== 'Sunday' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="{{ strtolower($day) }}_active">{{ $day }}</label>
                                    </div>
                                </div>
                                <div class="col-sm-3 time-select start">
                                    <select class="form-select">
                                        @foreach(range(8, 12) as $h)
                                        <option>{{ date("g:00a", strtotime("$h:00")) }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-sm-3 time-select end">
                                    <select class="form-select">
                                        @foreach(range(17, 22) as $h)
                                        <option>{{ date("g:00a", strtotime("$h:00")) }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-sm-3 closed-text d-none">
                                    <span class="text-muted">Closed</span>
                                </div>
                            </div>
                            @endforeach

                            <!-- FAQ Section -->
                            <div class="row mt-5">
                                <div class="col-12 d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5>FAQ’s</h5>
                                        <p>Add frequently ask questions here.</p>
                                    </div>
                                    <button type="button" class="btn_purple" data-bs-toggle="modal" data-bs-target="#exampleModal">Add FAQ</button>
                                </div>
                                <div class="accordion mt-3" id="faqAccordion">
                                    @foreach([
                                    ['question' => 'Are we insured?', 'answer' => 'Lorem ipsum...'],
                                    ['question' => 'What guarantees do we offer?'],
                                    ['question' => 'In what areas have we worked in?'],
                                    ['question' => 'Why choose us?']
                                    ] as $index => $faq)
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading{{ $index }}">
                                            <button class="accordion-button {{ $index > 0 ? 'collapsed' : '' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}">
                                                <i class="fa-solid fa-bars"></i> {{ $faq['question'] }}
                                            </button>
                                        </h2>
                                        <div id="collapse{{ $index }}" class="accordion-collapse collapse {{ $index === 0 ? 'show' : '' }}" data-bs-parent="#faqAccordion">
                                            <div class="accordion-body">
                                                {{ $faq['answer'] ?? '...' }}
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </form>
                    </div>

                    <!-- Password Tab -->
                    <div class="tab-pane fade" id="password" role="tabpanel">
                        <form>
                            <div class="row">
                                <div class="col-12 mb-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="page_title">
                                            <h1>Change Password</h1>
                                            <p>Update your password here.</p>
                                        </div>
                                        <div class="d-flex align-items-center gap-3">
                                            <button type="reset" class="btn_white">Cancel</button>
                                            <button type="submit" class="btn_purple">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Current Password -->
                            <div class="row mb-3">
                                <label for="passwords" class="col-sm-2 col-form-label">Password</label>
                                <div class="col-sm-8 position-relative">
                                    <input type="password" class="form-control" name="passwords" id="passwords">
                                    <i class="fa fa-eye toggle-password" toggle="#passwords"></i>
                                </div>
                            </div>

                            <!-- New Password -->
                            <div class="row mb-3">
                                <label for="new_password" class="col-sm-2 col-form-label">New Password</label>
                                <div class="col-sm-8 position-relative">
                                    <input type="password" class="form-control" name="new_password" id="new_password">
                                    <i class="fa fa-eye toggle-password" toggle="#new_password"></i>
                                </div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="row mb-3">
                                <label for="confirm_password" class="col-sm-2 col-form-label">Confirm Password</label>
                                <div class="col-sm-8 position-relative">
                                    <input type="password" class="form-control" name="confirm_password" id="confirm_password">
                                    <i class="fa fa-eye toggle-password" toggle="#confirm_password"></i>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel">Add FAQ</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="#">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="plan_name" class="form-label">Question</label>
                        <input type="text" class="form-control" name="plan_name" id="plan_name" placeholder="Enter question">
                    </div>
                    <div class="mb-3">
                        <label for="plan_price" class="form-label">Answer</label>
                        <input type="text" class="form-control" name="plan_price" id="plan_price" placeholder="Enter answer">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn_white" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn_purple">Add FAQ</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.17/js/intlTelInput.min.js"></script>

<script>
    $(document).ready(function() {
        // Dropify initialization
        $('.dropify').dropify({
            messages: {
                'default': 'Click to upload or drag and drop SVG, PNG, JPG or GIF (max. 800x400px)',
                'replace': 'Click to upload or drag and drop',
                'remove': 'Remove',
                'error': 'Ooops, something wrong happened.'
            },
            error: {
                'fileSize': 'The file size is too big (max 2MB).',
                'imageFormat': 'The image format is not allowed (PNG, JPG only).',
                'imageWidth': 'The image width must be under 800px.',
                'imageHeight': 'The image height must be under 400px.'
            }
        });
    });

    $(document).on('click', '.toggle-password', function() {
        var input = $($(this).attr("toggle"));
        var icon = $(this);
        if (input.attr("type") === "password") {
            input.attr("type", "text");
            icon.removeClass("fa-eye").addClass("fa-eye-slash");
        } else {
            input.attr("type", "password");
            icon.removeClass("fa-eye-slash").addClass("fa-eye");
        }
    });

    document.getElementById('country_select').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const flagCode = selectedOption.getAttribute('data-flag');
        document.getElementById('selected-flag').querySelector('img').src = `https://flagcdn.com/w40/${flagCode}.png`;
    });


    $(document).ready(function () {
        $('.day-checkbox').on('change', function () {
            const row = $(this).closest('.business-row');
            const checked = $(this).is(':checked');
            row.find('.time-select').toggle(checked);
            row.find('.closed-text').toggle(!checked);
        });

        $('.day-checkbox').each(function () {
            $(this).trigger('change');
        });
    });
</script>
@endpush