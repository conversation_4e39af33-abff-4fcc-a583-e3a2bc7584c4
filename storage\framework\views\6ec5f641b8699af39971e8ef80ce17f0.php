

<div class="row row-gap-5 pt-5 mt-3" id="cards-container">
    <?php for($i = 0; $i < 9; $i++): ?>
        <div class="col-lg-4 card-item" data-page="1">
        <div class="professional-card bg-white rounded shadow-sm overflow-hidden">
            <!-- Swiper -->
            <div class="swiper myimageSwiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <div class="position-relative">
                            <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" class="img-fluid w-100" alt="Professional">
                            <button class="wishlist-btn">
                                <i class="far fa-heart text-muted"></i>
                            </button>
                            <div class="security-badge position-absolute">
                                <span class="security-badge-inner d-flex gap-2 align-items-center fs-12 fw-600">
                                    <img src="<?php echo e(asset('website/assets/media/images/security-badge.svg')); ?>" width="12" height="12" alt="">
                                    TOP RATED
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <h6 class="text-midnight-navy fs-16 fw-600 mb-3">Build that dream patio</h6>

                <div class="d-flex align-items-center gap-2">
                    <div class="" style="width: 30px; height: 30px;">
                        <img class="img-fluid" src="<?php echo e(asset('website/assets/media/images/card-sec-logo.svg')); ?>" alt="">
                    </div>
                    <div>
                        <h6 class="text-midnight-navy fs-12 fw-600">Summit Engineering</h6>
                        <div class="d-flex align-items-center fs-12">
                            <i class="fas fa-star text-black"></i>
                            <p class="ms-1 fs-12 text-dark-black fw-600">5.0</p>
                            <p class="ms-1 fs-12 reddish-orange fw-600">(546)</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">Contractor</span>
                </div>
            </div>
            <div class="card_footer d-flex justify-content-between ">
                <div class="star_from">
                    <p class="fs-14 dusk-blue fw-500"> Start From</p>
                    <p class="fs-17 fw-700 text-midnight-navy">$100</p>
                </div>
                <a href="#" class="btn btn-quote text-white fs-13 fw-600">Get Quote</a>
            </div>
        </div>
</div>
<?php endfor; ?>


<?php for($i = 0; $i < 9; $i++): ?>
    <div class="col-lg-4 card-item" data-page="2" style="display: none;">
    <div class="professional-card bg-white rounded shadow-sm overflow-hidden">
        <!-- Swiper -->
        <div class="swiper myimageSwiper">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <div class="position-relative">
                        <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" class="img-fluid w-100" alt="Professional">
                        <button class="wishlist-btn">
                            <i class="far fa-heart text-muted"></i>
                        </button>
                        <div class="security-badge position-absolute">
                            <span class="security-badge-inner d-flex gap-2 align-items-center fs-12 fw-600">
                                <img src="<?php echo e(asset('website/assets/media/images/security-badge.svg')); ?>" width="12" height="12" alt="">
                                TOP RATED
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-3">
            <h6 class="text-midnight-navy fs-16 fw-600 mb-3">Professional Kitchen Design</h6>

            <div class="d-flex align-items-center gap-2">
                <div class="" style="width: 30px; height: 30px;">
                    <img class="img-fluid" src="<?php echo e(asset('website/assets/media/images/card-sec-logo.svg')); ?>" alt="">
                </div>
                <div>
                    <h6 class="text-midnight-navy fs-12 fw-600">Kitchen Masters</h6>
                    <div class="d-flex align-items-center fs-12">
                        <i class="fas fa-star text-black"></i>
                        <p class="ms-1 fs-12 text-dark-black fw-600">4.8</p>
                        <p class="ms-1 fs-12 reddish-orange fw-600">(324)</p>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">Designer</span>
            </div>
        </div>
        <div class="card_footer d-flex justify-content-between ">
            <div class="star_from">
                <p class="fs-14 dusk-blue fw-500"> Start From</p>
                <p class="fs-17 fw-700 text-midnight-navy">$150</p>
            </div>
            <a href="#" class="btn btn-quote text-white fs-13 fw-600">Get Quote</a>
        </div>
    </div>
    </div>
    <?php endfor; ?>

    
    <?php for($i = 0; $i < 9; $i++): ?>
        <div class="col-lg-4 card-item" data-page="3" style="display: none;">
        <div class="professional-card bg-white rounded shadow-sm overflow-hidden">
            <!-- Swiper -->
            <div class="swiper myimageSwiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <div class="position-relative">
                            <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>" class="img-fluid w-100" alt="Professional">
                            <button class="wishlist-btn">
                                <i class="far fa-heart text-muted"></i>
                            </button>
                            <div class="security-badge position-absolute">
                                <span class="security-badge-inner d-flex gap-2 align-items-center fs-12 fw-600">
                                    <img src="<?php echo e(asset('website/assets/media/images/security-badge.svg')); ?>" width="12" height="12" alt="">
                                    TOP RATED
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <h6 class="text-midnight-navy fs-16 fw-600 mb-3">Home Renovation Expert</h6>

                <div class="d-flex align-items-center gap-2">
                    <div class="" style="width: 30px; height: 30px;">
                        <img class="img-fluid" src="<?php echo e(asset('website/assets/media/images/card-sec-logo.svg')); ?>" alt="">
                    </div>
                    <div>
                        <h6 class="text-midnight-navy fs-12 fw-600">Renovation Pro</h6>
                        <div class="d-flex align-items-center fs-12">
                            <i class="fas fa-star text-black"></i>
                            <p class="ms-1 fs-12 text-dark-black fw-600">4.9</p>
                            <p class="ms-1 fs-12 reddish-orange fw-600">(687)</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="badge bg-light text-midnight-navy py-2 px-3 rounded-pill fs-14">Contractor</span>
                </div>
            </div>
            <div class="card_footer d-flex justify-content-between ">
                <div class="star_from">
                    <p class="fs-14 dusk-blue fw-500"> Start From</p>
                    <p class="fs-17 fw-700 text-midnight-navy">$200</p>
                </div>
                <a href="#" class="btn btn-quote text-white fs-13 fw-600">Get Quote</a>
            </div>
        </div>
        </div>
        <?php endfor; ?>
        </div>

        
        <div class="d-flex justify-content-center mt-5">
            <nav aria-label="Page navigation">
                <ul class="pagination pagination-custom">
                    <li class="page-item disabled" id="prev-btn">
                        <a class="page-link" href="javascript:void(0)" data-page="prev">« Prev</a>
                    </li>
                    <li class="page-item active" data-page="1">
                        <a class="page-link" href="javascript:void(0)" data-page="1">1</a>
                    </li>
                    <li class="page-item" data-page="2">
                        <a class="page-link" href="javascript:void(0)" data-page="2">2</a>
                    </li>
                    <li class="page-item" data-page="3">
                        <a class="page-link" href="javascript:void(0)" data-page="3">3</a>
                    </li>
                    <li class="page-item" id="next-btn">
                        <a class="page-link" href="javascript:void(0)" data-page="next">Next »</a>
                    </li>
                </ul>
            </nav>
        </div>

        <style>
            .pagination-custom {
                gap: 8px;
            }

            .pagination-custom .page-link {
                border: 2px solid #e9ecef;
                color: #6c757d;
                padding: 12px 18px;
                margin: 0;
                border-radius: 10px;
                text-decoration: none;
                transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                font-weight: 600;
                position: relative;
                overflow: hidden;
            }

            .pagination-custom .page-link::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background-color: #9B6CD6;
                transition: left 0.5s;
            }

            .pagination-custom .page-link:hover::before {
                left: 100%;
            }
         .pagination-custom .page-link:hover {
                background-color:#9B6CD6 ;
                color: var(--white);
                transform: translateY(-3px) scale(1.05);
            } 

            .pagination-custom .page-item.active .page-link {
                background-color: #9B6CD6;
                color: var(--white);
                transform: translateY(-2px);
            }

            .pagination-custom .page-item.active .page-link:hover {
                background-color: transparent;
                color: #9B6CD6;
                transform: translateY(-3px) scale(1.05);
            }

            .pagination-custom .page-item.disabled .page-link {
                color: #adb5bd;
                background-color: #f8f9fa;
                border-color: #e9ecef;
                cursor: not-allowed;
                opacity: 0.5;
                transform: none !important;
            }

            .pagination-custom .page-item.disabled .page-link:hover {
                background-color: #f8f9fa;
                border-color: #e9ecef;
                color: #adb5bd;
                transform: none !important;
                box-shadow: none !important;
            }

            /* .pagination-loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 20px;
                height: 20px;
                margin: -10px 0 0 -10px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            } */

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            .page-transition {
                overflow: hidden;
                position: relative;
            }

            .page-transition::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
                z-index: 1000;
                animation: pageSlide 0.8s ease-in-out;
            }

            @keyframes pageSlide {
                0% {
                    left: -100%;
                }

                50% {
                    left: 0;
                }

                100% {
                    left: 100%;
                }
            }
        </style>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

        <script>
            $(document).ready(function() {
                let currentPage = 1;
                const totalPages = 3;

                showPage(1);

                $('.pagination-custom .page-link').on('click', function(e) {
                    e.preventDefault();

                    const clickedPage = $(this).data('page');
                    let targetPage = currentPage;

                    if (clickedPage === 'prev') {
                        targetPage = currentPage > 1 ? currentPage - 1 : currentPage;
                    } else if (clickedPage === 'next') {
                        targetPage = currentPage < totalPages ? currentPage + 1 : currentPage;
                    } else if (typeof clickedPage === 'number') {
                        targetPage = clickedPage;
                    }

                    if (targetPage !== currentPage) {
                        changePage(targetPage);
                    }
                });

                function changePage(page) {
                    if (page < 1 || page > totalPages) return;

                    $('.card-item').hide();
                    $(`.card-item[data-page="${page}"]`).show();

                    updatePaginationButtons(page);

                    currentPage = page;
                }

                function updatePaginationButtons(page) {
                    $('.pagination-custom .page-item').removeClass('active');

                    $(`.pagination-custom .page-item[data-page="${page}"]`).addClass('active');

                    if (page === 1) {
                        $('#prev-btn').addClass('disabled');
                    } else {
                        $('#prev-btn').removeClass('disabled');
                    }

                    if (page === totalPages) {
                        $('#next-btn').addClass('disabled');
                    } else {
                        $('#next-btn').removeClass('disabled');
                    }
                }

                function showPage(page) {
                    $('.card-item').hide();
                    $(`.card-item[data-page="${page}"]`).show();
                    updatePaginationButtons(page);
                }
            });
        </script><?php /**PATH D:\Github\vadu\resources\views/website/templates/viewTemplate/contrator-card-view.blade.php ENDPATH**/ ?>