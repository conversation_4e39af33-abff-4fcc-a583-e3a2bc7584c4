<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Setting;
use App\Models\Profile;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CreateAdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $developer = User::firstOrCreate(array(
            'email' => '<EMAIL>',
            'name' => 'Developer'
        ));

        $developer->password = bcrypt("nmdp7788");
        $developer->save();

        if ($developer->profile == null) {
            $profile = new Profile();
            $profile->user_id = $developer->id;
            $profile->pic = 'no_avatar.jpg';
            $profile->save();
        }

        $admin = User::firstOrCreate(array(
            'email' => '<EMAIL>',
            'name' => 'Admin'
        ));

        $admin->password = bcrypt("nmdp7788");
        $admin->save();

        if ($admin->profile == null) {
            $profile = new Profile();
            $profile->user_id = $admin->id;
            $profile->pic = 'no_avatar.jpg';
            $profile->save();
        }

        $professioanl = User::firstOrCreate(array(
            'email' => '<EMAIL>',
            'name' => 'Professioanl'
        ));

        $professioanl->password = bcrypt("nmdp7788");
        $professioanl->save();

        if ($professioanl->profile == null) {
            $profile = new Profile();
            $profile->user_id = $professioanl->id;
            $profile->pic = 'no_avatar.jpg';
            $profile->save();
        }

        $customer = User::firstOrCreate(array(
            'email' => '<EMAIL>',
            'name' => 'Customer'
        ));

        $customer->password = bcrypt("nmdp7788");
        $customer->save();

        if ($customer->profile == null) {
            $profile = new Profile();
            $profile->user_id = $customer->id;
            $profile->pic = 'no_avatar.jpg';
            $profile->save();
        }

        $developer_role = Role::firstOrcreate(['name' => 'developer']);
        $admin_role = Role::firstOrcreate(['name' => 'admin']);
        $professioanl_role = Role::firstOrcreate(['name' => 'professioanl']); 
        $customer_role = Role::firstOrcreate(['name' => 'customer']);

        // Get existing permissions from the PermissionTableSeeder
        // $permissions = Permission::whereIn('name', [
        //     'crud-list', 'crud-create', 'crud-edit', 'crud-delete',
        //     'user-list', 'user-create', 'user-edit', 'user-delete',
        //     'role-list', 'role-create', 'role-edit', 'role-delete'
        // ])->get();
        $permissions = Permission::pluck('id', 'id')->all();

        // Assign roles and permissions
        $developer->assignRole('developer');
        $developer_role->syncPermissions($permissions);
        $admin->assignRole('admin');
        $professioanl->assignRole('professioanl');

        Setting::create(['title' => 'Update Title from admin', 'description' => 'Update Title from admin use for meta description tag', 'favicon' => 'AdminDashboard/default_logo.png', 'logo' => 'AdminDashboard/default-dark.svg', 'footer_text' => date('Y') . ' © Update footer form admin.']);
    }
}
