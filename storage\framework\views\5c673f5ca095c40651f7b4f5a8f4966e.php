<?php $__env->startPush('css'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.17/css/intlTelInput.css" />
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<section class="settings">
    <div class="container">
        <div class="row">
            <div class="col-12 mb-4">
                <h1>Settings</h1>

                <!-- Tabs -->
                <ul class="nav nav-tabs mt-4" id="settingsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">Profile</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password" type="button" role="tab">Password</button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content pt-4" id="settingsTabContent">
                    <!-- Profile Tab -->
                    <div class="tab-pane fade show active" id="profile" role="tabpanel">
                        <form>
                            <div class="row">
                                <div class="col-12 mb-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="page_title">
                                            <h1>Personal info</h1>
                                            <p>Update your photo and personal details here.</p>
                                        </div>
                                        <div class="d-flex align-items-center gap-3">
                                            <button type="reset" class="btn_white">Cancel</button>
                                            <button type="submit" class="btn_purple">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="name" class="col-sm-2 col-form-label">Name</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" name="name" id="name">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="email" class="col-sm-2 col-form-label">Email</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <div class="input-group-text bg-white"><i class="fa-regular fa-envelope"></i></div>
                                        <input type="email" name="email" class="form-control" id="email" placeholder="email">
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="phone_number" class="col-sm-2 col-form-label">Phone Number</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <div class="input-group-text bg-white"><i class="fa-solid fa-phone"></i></div>
                                        <input type="tel" class="form-control" name="phone_number" id="phone_number">
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="address" class="col-sm-2 col-form-label">Address</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <div class="input-group-text bg-white"><i class="fa-solid fa-location-dot"></i></div>
                                        <input type="text" class="form-control" name="address" id="address">
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="photo" class="col-sm-2 col-form-label">Your photo</label>
                                <div class="col-sm-8">
                                    <input name="photo" type="file" class="dropify"
                                        data-height="126"
                                        data-max-file-size="2M"
                                        data-allowed-file-extensions="png jpg jpeg"
                                        data-max-width="800"
                                        data-max-height="400" />
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label for="country" class="col-sm-2 col-form-label">Country</label>
                                <div class="col-sm-8">
                                    <div class="country-select-wrapper">
                                        <div class="flag-icon" id="selected-flag">
                                            <img src="https://flagcdn.com/w40/us.png" alt="Flag">
                                        </div>
                                        <select id="country_select" name="country" class="country-select form-select">
                                            <option data-flag="us" value="United States" selected>United States</option>
                                            <option data-flag="gb" value="United Kingdom">United Kingdom</option>
                                            <option data-flag="in" value="India">India</option>
                                            <option data-flag="pk" value="Pakistan">Pakistan</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Password Tab -->
                    <div class="tab-pane fade" id="password" role="tabpanel">
                        <form>
                            <div class="row">
                                <div class="col-12 mb-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="page_title">
                                            <h1>Change Password</h1>
                                            <p>Update your password here.</p>
                                        </div>
                                        <div class="d-flex align-items-center gap-3">
                                            <button type="reset" class="btn_white">Cancel</button>
                                            <button type="submit" class="btn_purple">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Current Password -->
                            <div class="row mb-3">
                                <label for="passwords" class="col-sm-2 col-form-label">Password</label>
                                <div class="col-sm-8 position-relative">
                                    <input type="password" class="form-control" name="passwords" id="passwords">
                                    <i class="fa fa-eye toggle-password" toggle="#passwords"></i>
                                </div>
                            </div>

                            <!-- New Password -->
                            <div class="row mb-3">
                                <label for="new_password" class="col-sm-2 col-form-label">New Password</label>
                                <div class="col-sm-8 position-relative">
                                    <input type="password" class="form-control" name="new_password" id="new_password">
                                    <i class="fa fa-eye toggle-password" toggle="#new_password"></i>
                                </div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="row mb-3">
                                <label for="confirm_password" class="col-sm-2 col-form-label">Confirm Password</label>
                                <div class="col-sm-8 position-relative">
                                    <input type="password" class="form-control" name="confirm_password" id="confirm_password">
                                    <i class="fa fa-eye toggle-password" toggle="#confirm_password"></i>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.17/js/intlTelInput.min.js"></script>

<script>
    $(document).ready(function() {
        // Dropify initialization
        $('.dropify').dropify({
            messages: {
                'default': 'Click to upload or drag and drop SVG, PNG, JPG or GIF (max. 800x400px)',
                'replace': 'Click to upload or drag and drop',
                'remove': 'Remove',
                'error': 'Ooops, something wrong happened.'
            },
            error: {
                'fileSize': 'The file size is too big (max 2MB).',
                'imageFormat': 'The image format is not allowed (PNG, JPG only).',
                'imageWidth': 'The image width must be under 800px.',
                'imageHeight': 'The image height must be under 400px.'
            }
        });
    });

    $(document).on('click', '.toggle-password', function() {
        var input = $($(this).attr("toggle"));
        var icon = $(this);
        if (input.attr("type") === "password") {
            input.attr("type", "text");
            icon.removeClass("fa-eye").addClass("fa-eye-slash");
        } else {
            input.attr("type", "password");
            icon.removeClass("fa-eye-slash").addClass("fa-eye");
        }
    });

    document.getElementById('country_select').addEventListener('change', function () {
    const selectedOption = this.options[this.selectedIndex];
    const flagCode = selectedOption.getAttribute('data-flag');
    document.getElementById('selected-flag').querySelector('img').src = `https://flagcdn.com/w40/${flagCode}.png`;
  });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views\dashboard\profile_settings.blade.php ENDPATH**/ ?>