<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('website.templates.banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <section class="listing-archive contractors py-10">
        <div class="container">
            <div class="row">
                <div class="col-md-3 filter-sec">
                    <div class="filter-container sticky trans-accordion">
                        <div class="filter-header">
                            <div class="filter-title">
                                <svg class="filter-icon" viewBox="0 0 16 16" fill="currentColor">
                                    <path
                                        d="M6 10.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z" />
                                </svg>
                                Filter
                            </div>
                            <a href="#" class="reset-filter">Reset Filter</a>
                        </div>

                        <div class="form-group filter-item">
                            <label class="form-label filter-title">Search By Keyword</label>
                            <input type="text" class="search-input" placeholder="What are you looking for?">
                        </div>

                        <div class="accordion" id="accordionFilter">
                            <div class="accordion-item filter-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button filter-title py-0" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true"
                                        aria-controls="collapseOne">
                                        Type
                                    </button>
                                </h2>
                                <div id="collapseOne" class="accordion-collapse collapse show">
                                    <div class="accordion-body pb-0">
                                        <?php
                                            $types = ['Interior', 'Exterior', 'Both', 'Commercial', 'Residential'];
                                        ?>
                                        <div class="type-wrapper d-flex align-items-center  gap-3 flex-wrap">
                                            <?php $__currentLoopData = $types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="type-item">
                                                    <input type="checkbox" class="d-none type-checkbox"
                                                        id="type_<?php echo e($key); ?>" value="<?php echo e($type); ?>">
                                                    <label class="type-label"
                                                        for="type_<?php echo e($key); ?>"><?php echo e($type); ?></label>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item filter-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button filter-title py-0" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false"
                                        aria-controls="collapseTwo">
                                        Services
                                    </button>
                                </h2>
                                <div id="collapseTwo" class="accordion-collapse collapse show">
                                    <div class="accordion-body pb-0">
                                        <?php
                                            $services = [
                                                'Bathroom Remodeling',
                                                'Kitchen Remodeling',
                                                'Basement Finishing',
                                                'Flooring Installation',
                                                'Painting',
                                                'Drywall / Wall repair',
                                                'Lighting & Electrical Fixture',
                                                'Roofing',
                                                'Siding Installation',
                                                'Deck or Patio Construction',
                                                'Window & Door Replacement',
                                            ];
                                        ?>
                                        <div class="services-list">
                                            <div class="service-item all-services">
                                                <input type="checkbox" class="service-checkbox" id="all-services">
                                                <label for="all-services">All Services</label>
                                            </div>
                                            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="service-item">
                                                    <input type="checkbox" class="service-checkbox"
                                                        id="service_<?php echo e($key); ?>" value="<?php echo e($service); ?>">
                                                    <label class="type-label"
                                                        for="service_<?php echo e($key); ?>"><?php echo e($service); ?></label>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group budget-section filter-item">
                            <label class="form-label filter-title">Budget Range</label>
                            <div class="range-container">
                                <input type="range" class="price-range" min="5" max="210" value="125">
                                
                                <div class="price-display" id="priceDisplay">
                                    Price per sq. ft : $5 - $210
                                </div>
                            </div>
                        </div>

                        <button class="search-button">Search</button>
                    </div>
                </div>
                <div class="col-md-9 result-sec">
                    <div class="sorting-wrapper d-flex justify-content-between align-items-center gap-3">
                        <h6 class="heading">254 Title $ Escrow</h6>
                        <div class="d-flex justify-content-end align-items-center gap-3">
                            <div class="sort-filter d-flex gap-5 align-items-center">
                                <label class="form-label mb-0">Sort:</label>
                                <select class="sorting-select" data-control="select2" data-placeholder="Select an option">
                                    <option disabled selected>Price Per sq. ft:</option>
                                    <option value="1">Option 1</option>
                                    <option value="2">Option 1</option>
                                </select>
                            </div>
                            <ul class="nav nav-pills justify-content-end view-change" id="view-tab" role="tablist">
                                <li class="nav-item m-0" role="presentation">
                                    <button class="nav-link active grid-view " id="grid-tab" data-bs-toggle="pill"
                                        data-bs-target="#grid-view" type="button" role="tab" aria-controls="grid-view"
                                        aria-selected="true">
                                        <i class="fas fa-th-large"></i>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link list-view" id="list-tab" data-bs-toggle="pill"
                                        data-bs-target="#list-view" type="button" role="tab"
                                        aria-controls="list-view" aria-selected="false">
                                        <i class="fas fa-list-ul"></i>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="view-wrapper">
                        <div class="tab-content" id="view-tabContent">
                            <div class="tab-pane fade show active" id="grid-view" role="tabpanel"
                                aria-labelledby="list-tab" tabindex="0">
                                <?php echo $__env->make('website.templates.viewTemplate.contrator-card-view', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="tab-pane fade" id="list-view" role="tabpanel" aria-labelledby="list-tab"
                                tabindex="0">
                                <?php echo $__env->make('website.templates.viewTemplate.contrator-list-view', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            // Select All Services
            function toggleAllServices(checkbox) {
                const isChecked = $(checkbox).is(':checked');
                $('.service-item:not(.all-services) input[type="checkbox"]').prop('checked', isChecked);
            }

            function updatePrice(value) {
                $('#priceDisplay').text(`Price per sqft : $5 - $${value}`);

                // Update slider background
                const percentage = ((value - 5) / (210 - 5)) * 100;
                $('.price-range').css('background',
                    `linear-gradient(to right, #8B5FBF 0%, #8B5FBF ${percentage}%, #ddd ${percentage}%, #ddd 100%)`
                );
            }
            // REset Filter
            function resetFilters() {
                // Reset search input
                $('.search-input').val('');

                // Reset type 
                $('.type-wrapper input[type="checkbox"]').prop('checked', false);

                // Reset services
                $('.service-item:not(.all-services) input[type="checkbox"]').prop('checked', false);

                // Reset price slider
                $('.price-range').val(125);
                updatePrice(125);
            }
            // Search result in console
            function performSearch() {
                // Collect all filter values
                const filters = {
                    keyword: $('.search-input').val(),
                    type: $('.type-wrapper input[type="checkbox"]:checked').map(function() {
                        return $(this).val();
                    }).get(),
                    services: $('.service-item:not(.all-services) input[type="checkbox"]:checked').map(
                        function() {
                            return $(this).val();
                        }).get(),
                    maxPrice: $('.price-range').val()
                };

                console.log('Search filters:', filters);
                // alert('Search functionality would be implemented here. Check console for filter values.');
            }

            // Initialize price display

            updatePrice(125);

            $('.search-button').on('click', function() {
                performSearch();
            });
            $('.reset-filter').on('click', function() {
                resetFilters();
            });
            $('#all-services').on('change', function() {
                if ($('#all-services:checked')) {
                    toggleAllServices(this)
                }
            });
            $('input[type="range"]').on('change', function() {
                updatePrice(this.value);
            });

        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views\website\contractors.blade.php ENDPATH**/ ?>