<?php $__env->startSection('content'); ?>
<?php echo $__env->make('website.templates.banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<!-- <?php echo $__env->make('website.templates.service-summit', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> -->
<section class="listing-detail py-10">
    <div class="container">
        <div class="row">
            <div class="col-md-8 listing-content">
                <div class="card gap-5">
                    <div class="card-header border-0 flex-column p-0">
                        <h5 class="listing-title fs-28">Build that dream patio</h5>
                        <ul class="listing-meta d-flex gap-5 align-items-center list-unstyled">
                            <li class="meta-item">
                                <i class="fas fa-star"></i>
                                <span class="rate">5.0</span>
                                <span class="text-danger">(546 Reviews)</span>
                            </li>
                            <li class="meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span class="rate">Great Falls, Maryland</span>
                            </li>
                        </ul>
                    </div>
                    <div class="listing-image-slider">
                        <div class="swiper myimageSwiper">
                            <div class="swiper-wrapper">
                                <?php for($i = 0; $i < 5; $i++): ?>
                                    <div class="swiper-slide">
                                    <div class="position-relative">
                                        <img src="<?php echo e(asset('website/assets/media/images/acme-engineering-services.png')); ?>"
                                            class="img-fluid w-100" alt="Professional">
                                    </div>
                            </div>
                            <?php endfor; ?>
                        </div>
                        <div class="swiper-navigation"></div>
                    </div>
                </div>
                <div class="card-body px-0">
                    <div class="accordion trans-accordion" id="accordionFilter">
                        <div class="accordion-item service-overview">
                            <?php echo $__env->make('website.templates.listingDetail.service-overview', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                        <div class="accordion-item  gallery">
                            <?php echo $__env->make('website.templates.listingDetail.gallery', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                        <div class="accordion-item customer-review">
                            <?php echo $__env->make('website.templates.listingDetail.customer-review', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                        <div class="accordion-item faq">
                            <?php echo $__env->make('website.templates.listingDetail.faq', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 listing-info sticky">
            <div class="button-box box-shadow mb-5">
                <p class="fs-14 text-dusk-blue fw-400">Price may vary</p>
                <h4 class="fs-38 fw-700 mb-5 pb-3 d-flex align-items-center">$457 <span class="fs-16 text-dusk-blue fw-400">Per Sqft</span></h4>
                <button data-bs-toggle="modal" data-bs-target="#projectModal" class="btn-gridient fs-16 fw-500 mb-5 text-white">Start a chat</button>
                <button href="#" class="learn-more-btn fs-16 fw-500 text-midnight-navy">Contact Provider <i class="fa-solid fa-eye text-midnight-navy"></i> </button>
            </div>
            <div class="professional-card box-shadow">
                <h5 class="fw-600 text-midnight-slate mb-3">VADU Professional</h5>
                <div class="acme-vadu d-flex align-items-center gap-3">
                    <div class="aes_logo" style="width: 50px; height:50px;">
                        <img class="img-fluid" src="<?php echo e(asset('website/assets/media/images/card-engineering-logo.svg')); ?>" alt="">
                    </div>
                    <div class="ratings">
                        <p class="text-midnight-navy fw-600">Acm e Engineering Services</p>
                        <div class="rating-reviews d-flex align-items-center">
                            <i class="fas fa-star text-black"></i>
                            <p class="ms-1 fs-16 text-dark-black fw-600">5.0</p>
                            <p class="ms-1 fs-16 reddish-orange fw-600">(546)</p>
                        </div>
                    </div>
                </div>
                <div class="acma-text-listing">
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Been in Business for</p>
                        <p class="fs-16 fw-400 text-dusk-blue">22 years</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Located in</p>
                        <p class="fs-16 fw-400 text-dusk-blue">Great Falls, Maryland</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">No. of Listings</p>
                        <p class="fs-16 fw-400 text-dusk-blue">235</p>
                    </div>
                </div>
                <div class="summit-logo-inner pt-4">
                    <p class="fs-16 text-midnight-slate fw-500 mb-3">Social Media:</p>
                    <div class="summit-icon d-flex align-items-center">
                        <a href="#" class="me-2"><i class="fa-brands fa-facebook"></i></a>
                        <a href="#" class="me-2"><i class="fa-brands fa-instagram"></i></a>
                        <a href="#" class="me-2"><img src="<?php echo e(asset('website/assets/media/images/card-x-icon.svg')); ?>" width="30" height="30" alt=""></a>
                        <a href=""><i class="fa-brands fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="busniness-hours box-shadow mt-5">
                <h5 class="fw-600 text-midnight-slate mb-3">Business Hours</h5>
                <div class="py-3">
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Monday</p>
                        <p class="fs-16 fw-400 text-dusk-blue">9:30 AM - 7:00 PM</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Tuesday</p>
                        <p class="fs-16 fw-400 text-dusk-blue">9:30 AM - 7:00 PM</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Wednesday</p>
                        <p class="fs-16 fw-400 text-dusk-blue">9:30 AM - 7:00 PM</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Thursday</p>
                        <p class="fs-16 fw-400 text-dusk-blue">9:30 AM - 7:00 PM</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Friday</p>
                        <p class="fs-16 fw-400 text-dusk-blue">9:30 AM - 7:00 PM</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Saturday</p>
                        <p class="fs-16 fw-400 text-dusk-blue">9:30 AM - 7:00 PM</p>
                    </div>
                    <div class="acma-text d-flex justify-content-between align-items-center mt-4">
                        <p class="fs-16 fw-500 text-midnight-slate">Sunday</p>
                        <p class="fs-16 fw-400 vivid-red">9:30 AM - 7:00 PM</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">

        </div>
    </div>
    </div>
</section>

<?php echo $__env->make('website.templates.Modal.project-card', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script>
    var gallerySlider = new Swiper(".gallerySlider", {
        slidesPerView: 3.5,
        spaceBetween: 30,
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },
    });
    var reviewSlider = new Swiper(".reviewSlider", {
        slidesPerView: 1.4,
        spaceBetween: 30,
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views\website\service-detail.blade.php ENDPATH**/ ?>