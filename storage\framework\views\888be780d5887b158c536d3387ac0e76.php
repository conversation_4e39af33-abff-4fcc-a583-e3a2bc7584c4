<?php $__env->startSection('content'); ?>
<?php $__env->startPush('css'); ?>
<style>
    /* Chat Container Styles */
    .chat-container {
        height: 100vh;
        background: #f8f9fa;
        display: flex;
        font-family: "Archivo", sans-serif;
    }

    /* Left Sidebar - Users List */
    .chat-sidebar {
        width: 350px;
        background: var(--white);
        border-right: 1px solid var(--cloud-gray);
        display: flex;
        flex-direction: column;
    }

    .chat-header {
        padding: 20px;
        border-bottom: 1px solid var(--cloud-gray);
        background: var(--white);
    }

    .chat-header h4 {
        margin: 0;
        color: var(--dark-black);
        font-weight: 600;
        font-size: 18px;
    }

    .chat-search {
        padding: 15px 20px;
        border-bottom: 1px solid var(--cloud-gray);
    }

    .search-input-wrapper {
        position: relative;
    }

    .search-input-wrapper input {
        width: 100%;
        padding: 12px 40px 12px 15px;
        border: 1px solid var(--cloud-gray);
        border-radius: 8px;
        font-size: 14px;
        background: var(--ghost-white);
    }

    .search-input-wrapper input:focus {
        outline: none;
        border-color: var(--purple);
        box-shadow: 0 0 0 2px rgba(155, 108, 214, 0.1);
    }

    .search-input-wrapper i {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--dusk-blue);
    }

    /* Users List */
    .users-list {
        flex: 1;
        overflow-y: auto;
        padding: 10px 0;
    }

    .user-item {
        padding: 15px 20px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .user-item:hover {
        background: var(--ghost-white);
    }

    .user-item.active {
        background: var(--purple);
        color: var(--white);
    }

    .user-item.active .user-name,
    .user-item.active .user-message,
    .user-item.active .user-time {
        color: var(--white);
    }

    .user-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: var(--purple);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-weight: 600;
        font-size: 16px;
        flex-shrink: 0;
        position: relative;
    }

    .user-info {
        flex: 1;
        min-width: 0;
    }

    .user-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }

    .user-name {
        font-weight: 600;
        font-size: 14px;
        color: var(--dark-black);
        margin: 0;
    }

    .user-time {
        font-size: 12px;
        color: var(--dusk-blue);
    }

    .user-message {
        font-size: 13px;
        color: var(--dusk-blue);
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .user-badge {
        width: 8px;
        height: 8px;
        background: #00E510;
        border-radius: 50%;
        position: absolute;
        bottom: 2px;
        right: 2px;
        border: 2px solid var(--white);
    }

    /* Right Chat Area */
    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: var(--white);
    }

    .chat-main-header {
        padding: 20px 25px;
        border-bottom: 1px solid var(--cloud-gray);
        background: var(--white);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .chat-main-header .user-avatar {
        width: 50px;
        height: 50px;
    }

    .chat-main-header .user-info h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--dark-black);
    }

    .chat-main-header .user-info p {
        margin: 0;
        font-size: 13px;
        color: var(--dusk-blue);
    }

    .chat-main-header .chat-actions {
        margin-left: auto;
        display: flex;
        gap: 10px;
    }

    .chat-main-header .chat-actions button {
        background: none;
        border: none;
        padding: 8px;
        border-radius: 6px;
        color: var(--dusk-blue);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .chat-main-header .chat-actions button:hover {
        background: var(--ghost-white);
        color: var(--purple);
    }

    /* Messages Area */
    .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #fafafa;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .message {
        display: flex;
        align-items: flex-end;
        gap: 10px;
        max-width: 70%;
    }

    .message.sent {
        align-self: flex-end;
        flex-direction: row-reverse;
    }

    .message.received {
        align-self: flex-start;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--purple);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-weight: 600;
        font-size: 12px;
        flex-shrink: 0;
    }

    .message-content {
        background: var(--white);
        padding: 12px 16px;
        border-radius: 18px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .message.sent .message-content {
        background: var(--purple);
        color: var(--white);
        border-bottom-right-radius: 6px;
    }

    .message.received .message-content {
        border-bottom-left-radius: 6px;
    }

    .message-text {
        font-size: 14px;
        line-height: 1.4;
        margin: 0;
    }

    .message-time {
        font-size: 11px;
        opacity: 0.7;
        margin-top: 4px;
        text-align: right;
    }

    .message.received .message-time {
        text-align: left;
    }

    .file-message {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px;
        background: var(--ghost-white);
        border-radius: 8px;
        border: 1px solid var(--cloud-gray);
    }

    .file-icon {
        width: 40px;
        height: 40px;
        background: var(--purple);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
    }

    .file-info h6 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--dark-black);
    }

    .file-info p {
        margin: 0;
        font-size: 12px;
        color: var(--dusk-blue);
    }

    /* Message Input Area */
    .chat-input-area {
        padding: 20px 25px;
        border-top: 1px solid var(--cloud-gray);
        background: var(--white);
    }

    .chat-input-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        background: var(--ghost-white);
        border-radius: 25px;
        padding: 8px 15px;
        border: 1px solid var(--cloud-gray);
    }

    .chat-input-wrapper:focus-within {
        border-color: var(--purple);
        box-shadow: 0 0 0 2px rgba(155, 108, 214, 0.1);
    }

    .attachment-btn {
        background: none;
        border: none;
        color: var(--dusk-blue);
        padding: 8px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .attachment-btn:hover {
        background: var(--white);
        color: var(--purple);
    }

    .chat-input-wrapper input {
        flex: 1;
        border: none;
        outline: none;
        background: transparent;
        font-size: 14px;
        padding: 8px 0;
        color: var(--dark-black);
    }

    .chat-input-wrapper input::placeholder {
        color: var(--dusk-blue);
    }

    .send-btn {
        background: var(--purple);
        border: none;
        color: var(--white);
        padding: 8px 12px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .send-btn:hover {
        background: #8a4fd4;
        transform: scale(1.05);
    }

    .send-btn:disabled {
        background: var(--cloud-gray);
        cursor: not-allowed;
        transform: none;
    }

    /* Date Separator */
    .date-separator {
        text-align: center;
        margin: 20px 0;
        position: relative;
    }

    .date-separator::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--cloud-gray);
    }

    .date-separator span {
        background: #fafafa;
        padding: 0 15px;
        font-size: 12px;
        color: var(--dusk-blue);
        font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .chat-container {
            height: calc(100vh - 80px);
        }

        .chat-sidebar {
            width: 100%;
            position: absolute;
            z-index: 1000;
            height: 100%;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .chat-sidebar.active {
            transform: translateX(0);
        }

        .chat-main {
            width: 100%;
        }

        .message {
            max-width: 85%;
        }
    }
</style>
<?php $__env->stopPush(); ?>
<div class="chat-container">
    <!-- Left Sidebar - Users List -->
    <div class="chat-sidebar">
        <!-- Header -->
        <div class="chat-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4>Users</h4>
                <button class="btn btn-sm" style="background: none; border: none; color: var(--dusk-blue);">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- Search -->
        <div class="chat-search">
            <div class="search-input-wrapper">
                <input type="text" placeholder="Search users..." id="userSearch">
                <i class="fas fa-search"></i>
            </div>
        </div>

        <!-- Users List -->
        <div class="users-list" id="usersList">
            <!-- User Items will be populated here -->
        </div>
    </div>

    <!-- Right Chat Area -->
    <div class="chat-main">
        <!-- Chat Header -->
        <div class="chat-main-header" id="chatHeader">
            <div class="user-avatar">PB</div>
            <div class="user-info">
                <h5>Phoenix Baker</h5>
                <p>Online</p>
            </div>
            <div class="chat-actions">
                <button type="button">
                    <i class="fas fa-phone"></i>
                </button>
                <button type="button">
                    <i class="fas fa-video"></i>
                </button>
                <button type="button">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- Messages Area -->
        <div class="chat-messages" id="chatMessages">
            <!-- Messages will be populated here -->
        </div>

        <!-- Message Input -->
        <div class="chat-input-area">
            <div class="chat-input-wrapper">
                <button type="button" class="attachment-btn">
                    <i class="fas fa-paperclip"></i>
                </button>
                <input type="text" placeholder="Message" id="messageInput">
                <button type="button" class="send-btn" id="sendBtn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    // Sample users data
    const users = [
        {
            id: 1,
            name: 'Chance Workman',
            avatar: 'CW',
            lastMessage: 'Lorem ipsum dolor sit amet...',
            time: '1h',
            online: true,
            unread: 0
        },
        {
            id: 2,
            name: 'Lana Steiner',
            avatar: 'LS',
            lastMessage: 'Hey team, I\'ve finished with the requirements doc!',
            time: 'Thursday 11:40am',
            online: true,
            unread: 0
        },
        {
            id: 3,
            name: 'Corey Korsgaard',
            avatar: 'CK',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '1d',
            online: false,
            unread: 0
        },
        {
            id: 4,
            name: 'Alfonso Workman',
            avatar: 'AW',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '2d',
            online: false,
            unread: 0
        },
        {
            id: 5,
            name: 'Angel Saris',
            avatar: 'AS',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '2h',
            online: true,
            unread: 0
        },
        {
            id: 6,
            name: 'Terry Herwitz',
            avatar: 'TH',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '2h',
            online: false,
            unread: 0
        },
        {
            id: 7,
            name: 'Cristofor Passeequindici Arca...',
            avatar: 'CP',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '2h',
            online: false,
            unread: 0
        },
        {
            id: 8,
            name: 'Gustavo Philips',
            avatar: 'GP',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '2h',
            online: false,
            unread: 0
        },
        {
            id: 9,
            name: 'Brandon Philips',
            avatar: 'BP',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '2h',
            online: false,
            unread: 0
        },
        {
            id: 10,
            name: 'Omar Levin',
            avatar: 'OL',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '2h',
            online: false,
            unread: 0
        },
        {
            id: 11,
            name: 'Ahmad Schleifer',
            avatar: 'AS',
            lastMessage: 'Lorem ipsum dolor sit amet cons...',
            time: '3h',
            online: false,
            unread: 0
        }
    ];

    // Sample messages data
    const messages = [
        {
            id: 1,
            userId: 2,
            type: 'received',
            content: 'Hey team, I\'ve finished with the requirements doc!',
            time: 'Thursday 11:40am',
            avatar: 'LS'
        },
        {
            id: 2,
            userId: 2,
            type: 'received',
            content: 'Tech requirements.pdf',
            time: 'Thursday 11:40am',
            avatar: 'LS',
            isFile: true,
            fileSize: '1.2 MB'
        },
        {
            id: 3,
            userId: 2,
            type: 'received',
            content: 'Good timing — was just looking at this.',
            time: 'Thursday 11:44am',
            avatar: 'DW'
        },
        {
            id: 4,
            userId: 1,
            type: 'sent',
            content: 'Awesome! Thanks.',
            time: 'Thursday 11:41am',
            avatar: 'You'
        },
        {
            id: 5,
            userId: 2,
            type: 'received',
            content: 'Hey Olivia, can you please review the latest design when you can?',
            time: 'Friday 2:20pm',
            avatar: 'PB'
        }
    ];

    let currentUserId = 2; // Default to Lana Steiner
    let currentMessages = messages.filter(msg => msg.userId === currentUserId);

    // Initialize chat
    function initializeChat() {
        renderUsersList();
        renderMessages();
        setActiveUser(currentUserId);
    }

    // Render users list
    function renderUsersList() {
        const usersList = $('#usersList');
        usersList.empty();

        users.forEach(user => {
            const userItem = $(`
                <div class="user-item" data-user-id="${user.id}">
                    <div class="user-avatar">
                        ${user.avatar}
                        ${user.online ? '<div class="user-badge"></div>' : ''}
                    </div>
                    <div class="user-info">
                        <div class="user-header">
                            <h6 class="user-name">${user.name}</h6>
                            <span class="user-time">${user.time}</span>
                        </div>
                        <p class="user-message">${user.lastMessage}</p>
                    </div>
                </div>
            `);
            usersList.append(userItem);
        });
    }

    // Set active user
    function setActiveUser(userId) {
        $('.user-item').removeClass('active');
        $(`.user-item[data-user-id="${userId}"]`).addClass('active');

        const user = users.find(u => u.id === userId);
        if (user) {
            $('#chatHeader').html(`
                <div class="user-avatar">${user.avatar}</div>
                <div class="user-info">
                    <h5>${user.name}</h5>
                    <p>${user.online ? 'Online' : 'Offline'}</p>
                </div>
                <div class="chat-actions">
                    <button type="button">
                        <i class="fas fa-phone"></i>
                    </button>
                    <button type="button">
                        <i class="fas fa-video"></i>
                    </button>
                    <button type="button">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            `);
        }
    }

    // Render messages
    function renderMessages() {
        const chatMessages = $('#chatMessages');
        chatMessages.empty();

        // Add date separator
        chatMessages.append('<div class="date-separator"><span>Today</span></div>');

        currentMessages.forEach(message => {
            let messageHtml = '';

            if (message.isFile) {
                messageHtml = `
                    <div class="message ${message.type}">
                        <div class="message-avatar">${message.avatar}</div>
                        <div class="message-content">
                            <div class="file-message">
                                <div class="file-icon">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div class="file-info">
                                    <h6>${message.content}</h6>
                                    <p>${message.fileSize}</p>
                                </div>
                            </div>
                            <div class="message-time">${message.time}</div>
                        </div>
                    </div>
                `;
            } else {
                messageHtml = `
                    <div class="message ${message.type}">
                        <div class="message-avatar">${message.avatar}</div>
                        <div class="message-content">
                            <p class="message-text">${message.content}</p>
                            <div class="message-time">${message.time}</div>
                        </div>
                    </div>
                `;
            }

            chatMessages.append(messageHtml);
        });

        // Scroll to bottom
        chatMessages.scrollTop(chatMessages[0].scrollHeight);
    }

    // Send message
    function sendMessage() {
        const messageInput = $('#messageInput');
        const messageText = messageInput.val().trim();

        if (messageText === '') return;

        // Add message to current conversation
        const newMessage = {
            id: Date.now(),
            userId: currentUserId,
            type: 'sent',
            content: messageText,
            time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
            avatar: 'You'
        };

        currentMessages.push(newMessage);

        // Clear input
        messageInput.val('');

        // Re-render messages
        renderMessages();

        // Simulate response after 2 seconds
        setTimeout(() => {
            const responseMessage = {
                id: Date.now() + 1,
                userId: currentUserId,
                type: 'received',
                content: 'Thank you for your message!',
                time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
                avatar: users.find(u => u.id === currentUserId).avatar
            };

            currentMessages.push(responseMessage);
            renderMessages();
        }, 2000);
    }

    // Search users
    function searchUsers() {
        const searchTerm = $('#userSearch').val().toLowerCase();
        $('.user-item').each(function() {
            const userName = $(this).find('.user-name').text().toLowerCase();
            if (userName.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // Event listeners
    $(document).on('click', '.user-item', function() {
        const userId = parseInt($(this).data('user-id'));
        currentUserId = userId;
        currentMessages = messages.filter(msg => msg.userId === userId);
        setActiveUser(userId);
        renderMessages();
    });

    $('#sendBtn').on('click', sendMessage);

    $('#messageInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            sendMessage();
        }
    });

    $('#userSearch').on('input', searchUsers);

    // Initialize the chat
    initializeChat();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views/website/chat.blade.php ENDPATH**/ ?>