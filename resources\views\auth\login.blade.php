@extends('layouts.app')
@section('title', 'Login - Vadu')
@section('content')
<div class="auth_page">
    <div class="glass-card">
        <form class="form w-100" method="POST" action="{{ route('login') }}" id="loginForm">
            @csrf
            <div class="text-center mb-8">
                <a href="{{route('home')}}"><img class="mb-5" src="{{ asset('website/assets/images/white-logo.svg') }}" alt="Vadu Logo"></a>
                <h1 class="text-white fw-bold my-3">Log In to Vadu</h1>
                <p class="text-white fs-6">
                    Lorem ipsum dolor sit amet consectetur. Id quam in metus ac sed pellentesque quis egestas.
                </p>
            </div>
            <div class="fv-row mb-8 form-group">
                <label class="fs-14 fw-500 text-start mb-3 text-white" for="email">Email Address</label>
                <input id="email" type="email" placeholder="Enter Email Address" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" autofocus>
                @error('email')
                <span class="invalid-feedback" role="alert">{{ $message }}</span>
                @enderror
            </div>

            <div class="fv-row mb-3 form-group">
                <label class="fs-14 fw-500 text-start mb-3 text-white" for="password">Password</label>
                <div class="position-relative">
                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" placeholder=" Enter Password">
                    <button type="button" class="btn position-absolute top-50 end-0 translate-middle-y pe-3" id="togglePassword" style="border: none; background: transparent; z-index: 10;">
                        <i class="fas fa-eye text-muted" id="toggleIcon"></i>
                    </button>
                </div>
                @error('password')
                <span class="invalid-feedback" role="alert">{{ $message }}</span>
                @enderror
            </div>

            <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
                <div></div>
                <a href="{{ route('password.request') }}" class="text-white">Forgot Password?</a>
            </div>

            <div class="d-grid mb-6">
                <button type="submit" id="kt_sign_in_submit" class="btn-gradient">
                    <span class="indicator-label">Login</span>
                    <span class="indicator-progress">
                        Please Wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                </button>
            </div>

            <div class="text-white text-center fs-6">
                <span>Not a Member Yet? <a href="{{ route('register') }}" class="">Sign Up</a></span>
            </div>
        </form>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.21.0/jquery.validate.min.js"></script>
<script>
    $(document).ready(function() {
        $('#loginForm').validate({
            rules: {
                email: {
                    required: true,
                    email: true
                },
                password: {
                    required: true,
                    minlength: 8
                }
            },
            messages: {
                email: {
                    required: "Email is Required!!",
                    email: "Please Enter a Valid Email Address!!"
                },
                password: {
                    required: "Password is Required",
                    minlength: "Password must be at least 8 Characters!!"
                }
            },
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest(".form-group").append(error);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass("is-invalid");
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass("is-invalid");
            }
        });

        $('#togglePassword').click(function() {
            const $passwordInput = $('#password');
            const $icon = $('#toggleIcon');

            const isPassword = $passwordInput.attr('type') === 'password';
            $passwordInput.attr('type', isPassword ? 'text' : 'password');

            $icon.toggleClass('fa-eye fa-eye-slash');
        });
    });
</script>
@endpush