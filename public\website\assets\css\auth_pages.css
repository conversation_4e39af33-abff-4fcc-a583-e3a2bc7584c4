
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&family=Sora:wght@100..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Archivo:ital,wght@0,100..900;1,100..900&display=swap');

:root {
    --lavender-purple: #9B6CD6;
}

.levender-purple{color: var(--lavender-purple);}
.gray-text{color: gray;}
.light-white{color: #fff;opacity: 0.6;}

h3{font-size: 34px; font-weight: 600;}
h4{font-size: 20px; font-weight: 600;}
.fs-14{font-size: 14px;}
.fs-16{font-size: 16px;}
.fs-24{font-size: 24px;}
.fw-600{font-weight: 600;}

.max-100 { max-height: 100vh; overflow: hidden; }

.professional-acc-form #acc-form, .register-form { position: relative; margin-top: 20px }
.register-form {
  /* max-width: 446px; */
  width: 100%;padding-top:5em ;}
.professional-acc-form #acc-form fieldset { padding-bottom: 20px; position: relative }
.professional-acc-form .form-card { text-align: left }
.professional-acc-form #acc-form fieldset:not(:first-of-type), .register-form fieldset:not(:first-of-type) { display: none }

.professional-acc-form #acc-form input[type="text"], #acc-form input[type="email"], #acc-form input[type=tel], #acc-form input[type="number"], #acc-form input[type="date"], #acc-form input[type="url"], #acc-form textarea { padding: 14px 12px 14px 16px; border-radius: 10px; border: 1px solid #DCDDE8; background: #FFF; margin-bottom: 25px; margin-top: 2px; width: 100%; font-size: 14px; outline: unset; }


#acc-form input[type=tel]  {padding: 14px 21px 15px 65px;}
.iti--allow-dropdown{ width: 100%;}

.professional-acc-form #progressbar li {border-bottom: 8px solid; border-radius: 10px;}

.professional-acc-form label .lavender-purple { font-size: 16px; font-weight: 600; color: var(--lavender-purple); }

.professional-acc-form .card { z-index: 0; border: none; position: relative; }
.professional-acc-form #progressbar { margin-bottom: 30px; overflow: hidden; color: lightgrey; gap: 10px; display: flex;width: 650px;margin: auto; }
.professional-acc-form #progressbar .active { color: #032642; }
.professional-acc-form #progressbar li { list-style-type: none; font-size: 15px; width: 20%; float: left; position: relative; font-weight: 400; }
.professional-acc-form .fit-image { width: 100%; object-fit: cover; }
.professional-acc-form label{ font-size: 14px; font-weight: 500; color: #000;}

.professional-acc-form h2{color: #000; font-family: Sora; font-size: 34px; font-style: normal; font-weight: 600;}
.professional-acc-form    p{font-size: 14px; font-style: normal; font-weight: 400;}
.professional-acc-form ol, ul { padding-left: 0; }
.professional-acc-form .next, .professional-acc-form .submit {padding: 10px 42px; border-radius: 10px; background-image: linear-gradient(90deg, #EC9B6E 48.36%, #9D6FD9 100%); color: #FFF; font-size: 16px; border: none; cursor: pointer;}

.professional-acc-form i.previous.action-button-previous, .first-stepper-form i.previous.action-button-previous { cursor: pointer; border-radius: 50%; background: #F1F1F1; height: 45px; display: flex; width: 45px; text-align: center; justify-content: center; align-items: center; }
.professional-acc-form .google_btn, .outlook_btn{border-radius: 10px;     color: black; font-size: 14px;  font-weight: 600; border: 1px solid rgba(220, 221, 232, 0.87); padding: 10px 30px; background: #F9F9F9;}
.professional-acc-form .next, .first-stepper-form .next{cursor: pointer;}
.professional-acc-form .google_btn, .outlook_btn{border-radius: 10px; border: 1px solid rgba(220, 221, 232, 0.87); padding: 10px 30px; background: #F9F9F9;}


.professional-acc-form .next, .first-stepper-form .next{cursor: pointer;}

a{text-decoration: none;}


/* Error message hidden by default */
.error-message {display: none;color: red;font-size: 12px;margin-top: 5px;}

/* Error border on checkbox box */
.checkbox-box.error {border-color: red !important;background-color: #ffe6e6;}
.services-section .custom-checkbox input[type="checkbox"]:checked + .checkbox-box::after {content: "";position: absolute;top: 10px;right: 10px;background-image: url('../images/Checkbox-form.svg');background-repeat: no-repeat;background-size: contain;height: 20px;width: 20px;}


/* Custom Radio btns - Step 2 */


.services-section .custom-radio { position: relative; display: inline-block; width: 100%; }
.services-section .custom-radio input[type="radio"] { display: none; }
.services-section .radio-box {display: flex; flex-direction: column; align-items: flex-start; justify-content: center; position: relative; transition: border-color 0.3s, box-shadow 0.3s; }
.services-section .custom-radio input[type="radio"]:checked + .radio-box { border-color: #0033cc; }
.services-section .custom-radio input[type="radio"]:checked + .radio-box::after {content: ""; position: absolute; top: 10px; right: 10px; background-image: url(./Checkbox-form.svg); background-repeat: no-repeat; height: 20px; width: 20px; }
.services-section .label-text {margin-top: 10px;  font-weight: 500;}


/* Checkbox - step 2 */

.custom-checkbox-group { display: flex; flex-wrap: wrap; gap: 12px; background-color: #f9f9f9; padding: 16px; border-radius: 12px; }
.custom-checkbox { position: relative; }
.custom-checkbox input[type="checkbox"] { display: none; }

.checkbox-label { display: inline-flex; align-items: center; gap: 8px; border: 2px solid #ccc; border-radius: 20px; padding: 8px 16px; font-size: 14px; background-color: white; cursor: pointer; transition: all 0.3s; }
.checkbox-label::before { content: ""; width: 18px; height: 18px; border: 2px solid #ccc; border-radius: 5px; display: inline-block; transition: all 0.3s; background-color: white; }

.custom-checkbox input[type="checkbox"]:checked + .checkbox-label { border-color: #1c2c8f; color: #1c2c8f; font-weight: 500; }
input[type="checkbox"]:checked + .checkbox-label::before { background-image: url("../images/Checkbox-form.svg"); background-repeat: no-repeat; background-position: center; border: unset; }

.services-section .custom-radio input[type="radio"]:checked + .custom-radio { border-color: #1c2c8f; color: #1c2c8f; font-weight: 500; }

/* second checkboxes */

.services-section.custom-radio-group2{display: flex; flex-wrap: wrap; width: 100%; gap: 17px;}
.services-section .custom-radio {position: relative; display: inline-block;}
.services {height: 605px; overflow-y: auto; padding-right: 20px;}
.services::-webkit-scrollbar {
  width: 50px !important; /* adjust as needed */
}

.gray-card{border-radius: 8px;  border: 1px solid #EBECED1A; background: #FFFFFF1A; padding: 15px;}
.time-picker-calendar label.days{  display: flex;  height: 44px; gap: 10px; align-items: center;}
.gray-card-main .gray-card {width: 410px;}

.custom-radio input[type="checkbox"] { display: none; }
.services-section .custom-radio  input[type="checkbox"]:checked + .checkbox-label::before { background-image: url("../images/Checkbox-form.svg"); background-repeat: no-repeat; background-position: center; border: unset; }

/*Image Input holder*/

/* .image-input-empty  {background-image: url('../images/image_input_holder.svg');} */
/* [data-bs-theme="dark"] .image-input-placeholder {background-image: url('../images/image_input_holder.svg');} */
.image_label { display: flex; justify-content: space-between; width: 51%; align-items: center; margin-bottom: 3em; gap: 40px; }
.image-input [data-kt-image-input-action=remove], .create_profile .image-input [data-kt-image-input-action=cancel], .image-input [data-kt-image-input-action=remove] { position: absolute;  z-index: 1; left: 19em; top: 12em; border: 1px solid green; background: #F0F9F4; color: black; width: 123px; padding: 7px; display: flex; justify-content: center; }
.image-input.image-input-outline .image-input-wrapper {  border-radius: 50%; margin: -3px;}
.image-input [data-kt-image-input-action=change] { left: 19em; top: 4em; width: 243px; z-index: 1; }
.image-input-wrapper { background-image: url('../images/image_input_holder.svg'); background-size: cover; background-position: center; background-repeat: no-repeat; border-radius: 50%; width: 125px; height: 125px; }

#toggle-password i, #toggle-password-otp i { cursor: pointer;}

button.delete-block { border: 1px solid #FFF; background: darkred; color: #FFF; border-radius: 20px; padding: 10px 15px; }

.first-stepper-form {height: 100vh; background-image: url(../images/auth_bg2.png);background-repeat: no-repeat;background-size: cover;background-position: center;}
.first-stepper-form .step-1 img.next.action-button {   width: 25px;  height: 25px;filter: invert(1);}

.iti__flag-container { margin-left: 10px; }

/*Dropzone*/

.upload-box { padding: 30px; background: var(--white-smoke); cursor: pointer; border-radius: 4px; border: 1px dashed gray; background: #FFF; display: flex; justify-content: center; align-items: center; flex-direction: column; gap: 10px;}
.upload-box { border: 2px dashed #ccc; border-radius: 10px; padding: 25px; text-align: center; cursor: pointer; background-color: #f9f9f9; transition: background 0.3s ease; position: relative; }
.upload-box:hover { background-color: #eee; }
.upload-box img { width: 60px; height: 40px; }

/* Preview container for certificates (images) */
.add-file { display: block; font-size: 18px; margin-top: 10px; color: #555; }
.preview-container { display: flex; flex-wrap: wrap; margin-top: 15px; gap: 15px; height: 10px; }
.preview-box { width: 160px; height: 95px; border: 1px solid #ddd; border-radius: 10px; overflow: hidden; position: relative; background-color: #fff; box-shadow: 0 0 5px rgba(0, 0, 0, 0.1); top: -22.3em; left: 39%; }
.preview-box img { width: 100%; height: 100%; object-fit: cover; }

/* Preview for file uploads (PDFs, ZIPs, DOCs) */
.preview-box-file { display: flex; align-items: center; gap: 12px; border: 1px solid #ddd; padding: 12px; border-radius: 10px; background-color: #fff; box-shadow: 0 0 5px rgba(0,0,0,0.05); position: relative; width: 100%; max-width: 300px; }

/* Remove button for both */
.remove-image, .remove-file { position: absolute; top: 5px; right: 8px; background: red; color: white; border: none; border-radius: 50%; width: 22px; height: 22px; font-size: 14px; line-height: 20px; cursor: pointer; text-align: center; padding: 0; display: flex; align-items: center; justify-content: center; }
.upload-cert-btn{ padding: 7px 20px; border-radius: 20px; background: #020C87; color: #FFF;}
.cert-excep { display: flex; width: 200px; align-items: center; gap: 8px; margin-bottom: 2em; }
.addMoreBtn { border-radius: 10px; border: 1px dashed #020C87; background: #FFF; padding: 15px; margin-block: 2em; width: 100%;}

/*Timezone*/

.start-time .flatpickr-input, .end-time .flatpickr-input, .start-time1 .flatpickr-input, .end-time1 .flatpickr-input { border-radius: 10px; border: 1px solid #DCDDE8; background: #FFF; padding: 19px !important; width: 120px !important; }
.start-time { display: none ; }

/*Second stepper form*/

.login-side-image img{width: 100%; height: 100vh;object-fit: cover; object-position: top;}

.btn-gradient{font-family: 'Inter'; border-radius: 8px; background-image: linear-gradient(90deg, #EC9B6E 48.36%, #9D6FD9 100%); text-align: center; color: #FFF; border: none;height: 48px; width: 100%; font-size: 16px; font-weight: 600;align-content: center;}

.register-form input, .register-form select{border-radius: 8px; outline: none; border: 1px solid rgba(220, 221, 232, 0.87); height: 46px;}

input:focus{border-color: unset !important; box-shadow: none !important;}

.site_logo { display: flex;flex-direction: column;align-items: center;position: absolute;left: 50%;bottom: 0px;transform: translate(-50%, -20px); }
.site_logo ul { display: flex ; gap: 30px; margin-top: 2em;overflow: hidden; }
.site_logo ul li{font-size: 14px; color: #8193a0;}

.services-section .custom-radio input[type="radio"]:checked + .radio-box::after, .services-section .custom-radio input[type="checkbox"]:checked + .radio-box::after { content: ""; position: absolute; top: 10px; right: 10px; background-image: url(../images/Checkbox-form.svg); background-repeat: no-repeat; height: 20px; width: 20px; }

#toggle-password-otp i { padding-bottom: 6em; }

input::-webkit-outer-spin-button, input::-webkit-inner-spin-button { -webkit-appearance: none; margin: 0; }
input[type=checkbox] { accent-color: #020C87;}
/* body:has(.step-4:not([style*="display: none"])) .site_logo {display: none;} */
/* body:has(.step-4[style*="display: block"]) .site_logo {display: none;} */
body:has(.step-4[style*="display: block"]) .login-side-image img {height: 100%;}
body:has(.step-4[style*="display: block"]) .first-stepper-form {height: 100vh;}
body:has(.step-5[style*="display: block"]) .first-stepper-form {height: 100%;}
body:has(.step-5[style*="display: block"]) .site_logo {display: none;}


.first-stepper-form fieldset {margin: auto;}
.first-stepper-form .iti__flag-container {margin: 0;}
.first-stepper-form .iti--separate-dial-code .iti__selected-flag {background: #fff;border-radius: 8px;}

.circle-crop  { border-radius: 50% !important;}
.single-explore-item { background: #fff; border: 1px solid #edeff1; border-radius: 3px; margin-bottom: 25px; -webkit-transition: .3s linear; -moz-transition: .3s linear; -ms-transition: .3s linear; -o-transition: .3s linear; transition: .3s linear; }
.single-explore-img-info { position: absolute; bottom: -20px; left: 0; width: 100%; opacity: 0; visibility: hidden; -webkit-transition: .3s linear; -moz-transition: .3s linear; -ms-transition: .3s linear; -o-transition: .3s linear; transition: .3s linear; }
.single-explore-item:hover .single-explore-img-info{ opacity:1; visibility:visible; bottom:0px }
.single-explore-img-info button{ position: absolute; bottom: 15px; left: 15px; height: 21px; line-height: 21px; background: #ff545a; border-radius: 3px; color: #fcfcfc; text-transform: capitalize; text-align: center; font-size: 12px; }

.HolidayModal.modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center; z-index: 9999; }
.HolidayModal .modal-content { background: #fff; padding: 20px; border-radius: 10px; width: 300px; }
.HolidayModal .modal-content input { width: 100%; margin: 10px 0; padding: 8px; }
.HolidayModal .modal-content .close { float: right; font-size: 18px; cursor: pointer; }

button.add-custom-holiday-btn { border-radius: 8px; border: 1px solid #F0F0F0; padding: 8px 14px; color: #020C87; font-size: 16px; font-weight: 600; background: #FFF; margin-top: 1em; }

.exception-textarea { display: none;}
.exception-checkbox:has(input:checked) .exception-textarea { display: block;}

.services-section.custom-radio-group input[type="radio"]:checked + .radio-box { border: 2px solid #9B6CD6; border-radius: 6px; border-radius: 10px; background: #FFF; padding: 25px; }
.services-section.custom-radio-group span.radio-box { border-radius: 10px; border: 2px solid #DCDDE8; background: #FFF; padding: 25px; }

img.service-icon{width: 50px; height: 50px;}

/* Chexkox */
.custom-checkbox { display: inline-block; cursor: pointer; } .custom-checkbox input { display: none; }
.checkbox-box { display: flex; flex-direction: column; align-items: flex-start; border: 1px solid #ddd; border-radius: 10px; padding: 20px; width: 180px; height: 140px; position: relative; transition: all 0.3s ease; background-color: white; }
.label-text { font-size: 16px; font-weight: 500; color: #000; text-align: center; }



.auth_page {height: 100vh;display: flex;align-items: center;justify-content: center;background-image: url('../images/auth_bg.png');background-repeat: no-repeat;background-size: cover;background-position: center;}
.glass-card {width: 100%;max-width: 460px;padding: 40px;border-radius: 12px;box-shadow: 2px 16px 19px 0px rgba(0, 0, 0, 0.09);backdrop-filter: blur(80px);border: 1px solid #EC9B6E80;background: radial-gradient(90.16% 143.01% at 15.32% 21.04%, rgba(224, 249, 255, 0.2) 0%, rgba(110, 191, 244, 0.0447917) 77.08%, rgba(70, 144, 213, 0) 100%);}
.register-form h3 {color: #fff;font-size: 24px;font-weight: 700;margin-bottom: 15px}
.register-form p {color: #fff;font-size: 14px;font-weight: 400;}
.register-form label {
    color: #fff;
}

.card.subscription {border-radius: 12px;border: 1px solid #E9EAEB;}
.card.subscription ul {list-style: none;}
.card.subscription h6 {font-family: 'Inter';font-size: 17px;font-weight: 600;color: #181D27;margin-bottom: 10px;}
.card.subscription h2 {font-family: 'Inter';font-size: 48px;font-weight: 600;color: #181D27;margin-bottom: 10px;}
.card.subscription h2 span { font-size: 20px; font-weight: 400; font-family: 'Inter'; }
.card.subscription p {font-family: 'Inter';font-size: 13px;font-weight: 400;color: #535862;margin-bottom: 20px;}
.card.subscription ul li i {color: #181D27;font-size: 20px;margin-right: 10px;}
.card.subscription ul li {margin-bottom: 10px;font-family: 'Inter';font-size: 13px;font-weight: 400;color: #535862;}


/* Opening-Hours-Step-5 */
.bh-time-dropdown:focus-visible {outline: 0;}
.bh-day-item:last-child { border-bottom: none; }
.bh-closed-label { color: #999; font-size: 14px; }
.bh-time-wrapper { display: flex; align-items: center; gap: 8px; }
.bh-day-label { display: flex; align-items: center; margin: 0; cursor: pointer; }
.bh-day-label input { margin-right: 8px; accent-color: #8b5cf6;    width: 16px;height: 16px; }
.bh-day-item { display: flex; justify-content: space-between; align-items: center; padding: 20px 0;}
.bh-main-container { border-radius: 10px; border: 1px solid #EBECED; background: #FFF; padding: 30px; }
/* .bh-gray-section { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; }      */
.bh-time-dropdown:focus { outline: none; border-color: #8b5cf6; box-shadow: 0 0 0 2px rgba(139,92,246,0.2); }
.bh-time-dropdown {     border-radius: 10px;border: 1px solid #DCDDE8;background: #FFF;padding: 14px 12px 14px 16px; }
.bh-save-button { background: #8b5cf6; border: none; color: white; padding: 10px 20px; border-radius: 6px; transition: all 0.2s; }
.bh-save-button:hover { background: #7c3aed; transform: translateY(-1px); }.bh-success-alert { background: #f0fdf4; border: 1px solid #bbf7d0; color: #166534; border-radius: 6px; padding: 12px; margin-bottom: 15px; display: none; }



        
.pur-review-section {text-align: center;}
.pur-clock-icon {width: 80px;height: 80px;margin: 0 auto 20px;position: relative;}
.pur-clock-circle { width: 80px;height: 80px;border-radius: 50%;position: relative;transform: rotate(-90deg);}
.pur-clock-bg {stroke: #fed7aa;stroke-width: 6;fill: none;stroke-dasharray: 10 5; stroke-dashoffset: 0;}
/* .pur-clock-progress { stroke: #1f2937; stroke-width: 6; fill: none; stroke-dasharray: 220; stroke-dashoffset: 55; stroke-linecap: round; transition: stroke-dashoffset 2s ease-in-out; } */
.pur-clock-hands { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(90deg); width: 40px; height: 40px; }
.pur-hour-hand { position: absolute; width: 3px; height: 15px; background: #1f2937; border-radius: 2px; top: 5px; left: 50%; transform: translateX(-50%) rotate(45deg); transform-origin: bottom; }
.pur-minute-hand { position: absolute; width: 2px; height: 20px; background: #1f2937; border-radius: 1px; top: 0; left: 50%; transform: translateX(-50%) rotate(90deg); transform-origin: bottom; }
.pur-center-dot { position: absolute; width: 4px; height: 4px; background: #1f2937; border-radius: 50%; top: 50%; left: 50%; transform: translate(-50%, -50%); }
.pur-title { font-weight: 600; color: #032642; margin-bottom: 12px; font-size: 34px; font-family: Archivo; }.pur-subtitle { color: #6b7280; font-size: 16px; line-height: 1.5; margin-bottom: 30px; }
.pur-subtitle { color: #656B76; font-family: 'Archivo'; }
@keyframes clockTick {0% { transform: translateX(-50%) rotate(90deg); }100% { transform: translateX(-50%) rotate(450deg); }}
.pur-minute-hand {animation: clockTick 4s ease-in-out infinite;}
@media (max-width: 576px) {.pur-main-container {padding: 20px 15px;}.pur-review-section {padding: 30px 20px;}
.pur-contact-section {padding: 25px 20px;}}



@media(min-width:1500px){
  .services { height: auto; overflow-y: unset; padding-right: unset; }
}