

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('website.templates.banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startPush('css'); ?>
<style>



</style>
<?php $__env->stopPush(); ?>

<section class="terms-condition py-15">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4 terms-sidebar">
                <ul class="terms-sidebar-nav text-dusk-blue">
                    <li><a href="#introduction" class="text-dusk-blue active">Introduction</a></li>
                    <li><a href="#feedback" class="text-dusk-blue">Feedback and Information</a></li>
                    <li><a href="#privacy" class="text-dusk-blue">Privacy</a></li>
                    <li><a href="#access" class="text-dusk-blue">Access to the site</a></li>
                    <li><a href="#age" class="text-dusk-blue">Age requirements</a></li>
                    <li><a href="#accounts" class="text-dusk-blue ">Accounts, Passwords and security</a></li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8 main-content">
                <div id="introduction" class="content-section">
                    <h5 class="fs-22 text-midnight-slate fw-500 section-title pb-7">Vadu Terms and Conditions</h5>
                    <h5 class="fs-16 text-midnight-slate fw-500 pb-5">Introduction</h5>

                    <p class="fs-14 text-dusk-blue">
                        These are the terms and conditions of use for www.vadu.com ('Terms') and all affiliated websites owned and operated by Vadu.com SV Ltd. (we, us and our),
                        including all subdomains, subdirectories, mobile sites, and mobile applications (collectively, the 'Site'). We are a limited company, registered in England. Our
                        registered company number is ********, and our registered office is at 71-75 Shelton Street, London, Greater London, WC2H 9JQ, United Kingdom. Our VAT
                        registration number is GB299786808.
                        <br>
                        <br>
                        Your use of the Site will be subject to these Terms and by using the Site you agree to be bound by them.
                        <br>
                        <br>
                        These terms and conditions of use only apply to your use of our Site. These terms and conditions of use DO NOT apply to the third party goods and services
                        which are available for booking and purchases on our Site. The online ordering and booking-related services (including payments, vouchers and bookings) we
                        make available through the Site (the 'Vadu Services') are provided subject to our <a href="#" class="highlight-link">Terms of Service</a>. Our third party partners fulfil these services (including the
                        provision of your selected treatments) (the 'Partner Services').
                        <br>
                        <br>
                        We may change these Terms from time to time by changing them on the Site. Your continued use of the Site following the posting of changes will mean that you
                        accept and agree to the changes. As long as you comply with these Terms of Use, Vadu grants you a personal, non-exclusive, non-transferable, limited privilege
                        to enter and use the Site. These Terms were last updated on 11 Feb 2022.
                    </p>
                </div>

                <div id="feedback" class="content-section">
                    <h5 class="fs-16 text-midnight-slate fw-500 py-5">Feedback and Information</h5>
                    <p class="fs-14 text-dusk-blue">Please submit any feedback you have about these Terms or any questions concerning the Site by <NAME_EMAIL>.</p>

                </div>

                <div id="privacy" class="content-section">
                    <h5 class="fs-16 text-midnight-slate fw-500 py-4">Privacy</h5>
                    <p class="fs-14 text-dusk-blue">If you use our Site, Vadu’s Privacy Policy applies and forms a part of these Terms of Use. Unfortunately, the transmission of information via the internet is not completely secure. We do our best to protect your personal information, but we cannot guarantee the security of your data transmitted to us – even if there is a special notice that a particular transmission (for example, credit card information) is encrypted. Any transmission is at your own risk.</p>
                </div>
                <div id="age" class="content-section">
                    <h5 class="fs-16 text-midnight-slate fw-500 py-4">Access to the site</h5>
                    <p class="fs-14 text-dusk-blue">If you use our Site, Vadu’s Privacy Policy applies and forms a part of these Terms of Use. Unfortunately, the transmission of information via the internet is not completely secure. We do our best to protect your personal information, but we cannot guarantee the security of your data transmitted to us – even if there is a special notice that a particular transmission (for example, credit card information) is encrypted. Any transmission is at your own risk.</p>
                </div>
                <div id="requirements" class="content-section">
                    <h5 class="fs-16 text-midnight-slate fw-500 py-4">Age requirements</h5>
                    <p class="fs-14 text-dusk-blue">If you use our Site, Vadu’s Privacy Policy applies and forms a part of these Terms of Use. Unfortunately, the transmission of information via the internet is not completely secure. We do our best to protect your personal information, but we cannot guarantee the security of your data transmitted to us – even if there is a special notice that a particular transmission (for example, credit card information) is encrypted. Any transmission is at your own risk.</p>
                </div>
                <div id="accounts" class="content-section">
                    <h5 class="fs-16 text-midnight-slate fw-500 py-4">Accounts, Passwords and security</h5>
                    <p class="fs-14 text-dusk-blue">If you use our Site, Vadu’s Privacy Policy applies and forms a part of these Terms of Use. Unfortunately, the transmission of information via the internet is not completely secure. We do our best to protect your personal information, but we cannot guarantee the security of your data transmitted to us – even if there is a special notice that a particular transmission (for example, credit card information) is encrypted. Any transmission is at your own risk.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    $(document).ready(function() {
        $('.terms-sidebar-nav a').on('click', function(e) {
            e.preventDefault();
            $('.terms-sidebar-nav a').removeClass('active');
            $(this).addClass('active');
            var targetId = $(this).attr('href');
            var targetSection = $(targetId);
            var offset = 100;
            $('html, body').stop().animate({
                scrollTop: targetSection.offset().top - offset
            }, 800, 'swing', function() {
                window.location.hash = targetId;
            });
        });

        $(window).on('scroll', function() {
            var scrollPosition = $(window).scrollTop() + 120;

            $('.content-section').each(function() {
                var sectionTop = $(this).offset().top;
                var sectionBottom = sectionTop + $(this).outerHeight();
                var sectionId = $(this).attr('id');

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    $('.terms-sidebar-nav a').removeClass('active');
                    $('.terms-sidebar-nav a[href="#' + sectionId + '"]').addClass('active');
                }
            });
        });
        if (window.location.hash) {
            var hash = window.location.hash;
            var targetSection = $(hash);

            if (targetSection.length) {
                $('.terms-sidebar-nav a').removeClass('active');
                $('.terms-sidebar-nav a[href="' + hash + '"]').addClass('active');

                setTimeout(function() {
                    $('html, body').animate({
                        scrollTop: targetSection.offset().top - 100
                    }, 500);
                }, 100);
            }
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views/website/terms-condition.blade.php ENDPATH**/ ?>