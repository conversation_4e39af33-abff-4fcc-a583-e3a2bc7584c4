@extends('theme.layout.master')

@section('content')

<section class="">
    <div class="container">
        <div class="row">
            <div class="col-12 mb-10">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="page_title">
                        <h1>Notifications</h1>
                    </div>
                </div>
            </div>
        </div>

        <div class="row py-4">
            <div class="col-md-12">
                <div class="table_container">
                    <div class="table_header">
                    <h5>Notifications</h5>
                    <div class="table_filter d-flex align-items-center justify-content-end gap-2">
                        <div class="search_container m-0">
                            <i class="fas fa-search search_icon"></i>
                            <input type="text" id="customSearch" placeholder="Search..." class="form-control">
                        </div>
                    </div>
                </div>


                    <table id="myTable" class="display table table-borderless">
                        <thead class="d-none">
                            <tr>
                                <th>Name</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @for ($i=0; $i<=10; $i++)

                                <tr>
                                <td>
                                    <div class="profile_card">
                                        <img class="rounded-circle" src="{{asset('website')}}/assets/images/avatar1.png" alt="user avatar" width="40">
                                        <div class="profile_info">
                                            <div class="name">Bathroom Remodeling</div>
                                            <p>Lorem ipsum dolor sit amet consectetur. Lorem rutrum dignissim leo leo adipiscing risus orci nibh odio.</p>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    1hr
                                </td>
                                </tr>


                                @endfor

                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>
</section>

@endsection

