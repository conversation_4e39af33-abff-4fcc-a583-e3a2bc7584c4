@extends('layouts.app')

@section('content')
<!--begin::Authentication - Sign-up -->

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 p-12 p-lg-10 first-stepper-form d-flex align-items-center justify-content-between flex-column">
            <form class="register-form form" novalidate="novalidate" method="POST" method="POST" action="{{ route('register') }}">
                <!-- <div>
                    <i name="previous" value="ll" class="fas fa-chevron-left previous action-button-previous"></i>
                </div> -->

                @csrf
                <fieldset class="step-1">
                    <a href="{{url('/')}}" class="text-center d-block mb-5">
                    <img src="{{asset('website')}}/assets/images/logo-white.svg" alt="icon"></a>
                    <h3 class="text-center mb-10 text-white fs-24">Sign up/Login for Vadu</h3>
                    <div class="gray-card d-flex justify-content-between align-items-center mb-5 next action-button" value="Next">
                        <div>
                            <h5 class="fs-16 fw-600 text-white">For customers</h5>
                            <p class="fs-14 gray-text m-0">Get your work done by professionals</p>
                        </div>
                        <img src="{{asset('website')}}/assets/images/right-arrow.svg" class="next action-button" value="Next" alt="icon">
                    </div>

                    <div class="gray-card d-flex justify-content-between align-items-center next action-button" value="Next">
                        <div>
                            <h5 class="fs-16 fw-600 text-white">For professionals</h5>
                            <p class="fs-14 gray-text m-0">Manage and grow your business</p>
                        </div>
                        <img src="{{asset('website')}}/assets/images/right-arrow.svg" class="next action-button" value="Next" alt="icon">
                    </div>
                </fieldset>

                <fieldset class="step-2 glass-card">
                    <div class="mb-10">
                        <a href="{{url('/')}}" class="text-center d-block mb-5">
                    <img src="{{asset('website')}}/assets/images/logo-white.svg" alt="icon"></a>
                        <h3 class="text-center">Vadu for professionals</h3>
                        <p class="fs-14 text-center">Create an account or log in to manage your business.</p>
                    </div>

                    <div class="mb-8">
                        <label class="fs-14 fw-500 text-start mb-3" for="email">Email Address</label>
                        <input id="email" type="email" placeholder="Enter your email address"
                            class="form-control mb-6 @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email">

                        @error('name')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror

                        <button type="button" class="next action-button btn-gradient" value="Next"> Continue
                        </button>
                    </div>
                </fieldset>

                <fieldset class="step-3 glass-card">
                    <div class="mb-10">
                        <a href="{{url('/')}}" class="text-center d-block mb-5">
                    <img src="{{asset('website')}}/assets/images/logo-white.svg" alt="icon"></a>
                        <h3 class="text-center">Verify your identity</h3>
                        <p class="fs-14 text-center">To protect your account, we'll send a text message with a 4-
                            digit code <NAME_EMAIL>.</p>
                    </div>

                    <div class="mb-8 position-relative">
                        <label class="fs-14 fw-500 text-start mb-3" for="otp">Enter OTP</label>
                        <input id="otp" type="number" placeholder="Enter OTP" class="form-control mb-6"
                            name="otp">

                        <span id="toggle-password-otp"
                            class=" btn-sm btn-icon position-absolute translate-middle end-0 pe-2">
                            <i class="fa-solid fa-eye"></i>
                            <i class="fa-solid fa-eye-slash d-none"></i>
                        </span>



                        <button type="button" class="next action-button btn-gradient" value="Next"> Verify OTP
                        </button>
                    </div>
                </fieldset>

                <fieldset class="step-4 glass-card">
                    <div class="row">

                        <div class="col-md-12 mb-10">
                            <a href="{{url('/')}}" class="text-center d-block mb-5">
                    <img src="{{asset('website')}}/assets/images/logo-white.svg" alt="icon"></a>
                            <h3 class="text-center">Create a customer account</h3>
                            <p class="fs-14 text-center">You're almost there! Create your new <NAME_EMAIL> by completing these details.</p>
                        </div>

                        <div class="col-md-12 mb-10">
                            <div class="Image-input_holder mb-10">
                                <div class="image-input image-input-empty" data-kt-image-input="true">
                                    <div class="image-input image-input-outline" data-kt-image-input="true">
                                        <div class="image-input-wrapper w-125px h-125px"></div>
                                        <label class="dark-green-btn fs-14 regular pt-9" data-kt-image-input-action="change">
                                            <span class="pe-3 fs-16 fw-600 mb-10 purple-text"> Upload Logo</span>
                                            <input type="file" name="avatar" accept=".png, .jpg, .jpeg" />
                                            <input type="hidden" name="avatar_remove" />
                                            <p class="fs-24 medium pt-4 gray-text"> At least 500x500 px recommended. JPG or PNG is allowed</p>
                                        </label>

                                        <!-- <a href="#!" class="light-green-btn fs-14 regular ms-5" data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                data-bs-dismiss="click" title="Cancel avatar"> <i   class="fas fa-times fa-5"></i> </a>

                            <a href="#!" class="light-green-btn fs-14 regular ms-5" data-kt-image-input-action="remove"> Remove </a> -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12 mb-5">
                            <label class="fs-14 fw-500 text-start mb-3" for="full_name">Full Name</label>
                            <input id="full_name" type="text" placeholder="Enter your full name" class="form-control" name="full_name" required>
                        </div>
                        <div class="col-md-12 mb-5">
                            <label class="fs-14 fw-500 text-start mb-3" for="email">Email Address</label>
                            <input id="email" type="email" placeholder="Enter email address" class="form-control" name="email" required>
                        </div>
                        <div class="col-md-12 mb-5">
                            <label class="fs-14 fw-500 text-start mb-3" for="phone">Phone</label>
                            <input id="phone" type="tel" placeholder="Enter phone number" class="form-control" name="phone" required>
                        </div>
                        <div class="col-md-12 mb-5">
                            <label class="fs-14 fw-500 text-start mb-3" for="country">Country</label>
                            <select id="country" class="form-select" aria-label="Select your country" name="country">
                                <option selected>Select your country</option>
                                <option value="1">One</option>
                                <option value="2">Two</option>
                                <option value="3">Three</option>
                            </select>
                        </div>
                        <div class="col-md-12 mb-5">
                            <label class="fs-14 fw-500 text-start mb-3" for="state">State</label>
                            <select id="state" class="form-select" aria-label="Select your state" name="state">
                                <option selected>Select your state</option>
                                <option value="1">One</option>
                                <option value="2">Two</option>
                                <option value="3">Three</option>
                            </select>
                        </div>

                        <!-- <div class="mb-10">
                        <label class="fs-14 fw-500 text-start mb-3">Enter Password</label>
                        <input id="password" type="password" placeholder="Password"
                            class="form-control mb-5 bg-transparent @error('password') is-invalid @enderror" name="password"
                            required autocomplete="new-password">
                        @error('password')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                        <span id="toggle-password"
                            class=" btn-sm btn-icon position-absolute translate-middle mb-8 end-0 pb-20 pe-2">
                            <i class="fa-solid fa-eye"></i>
                            <i class="fa-solid fa-eye-slash d-none"></i>
                        </span> -->

                        <!-- <button type="button" class="btn-gradient" value="Submit"> Login</button> -->

                        <!-- </div> -->
                        <div class="col-md-12 mb-5">

                            <a href="{{ url('professional_account') }}" class="btn-gradient w-100 d-block"> Login</a>
                        </div>
                    </div>
                </fieldset>

            </form>

            <div class="site_logo">
                <a href="{{url('/')}}" class="text-center">
                    <img src="{{asset('website')}}/assets/images/logo-white-footer.svg" alt="icon">
                    <!-- <h4 class="blue-text pt-2"> Stylenest </h4> -->
                </a>
                <ul>
                    <li> <a href="{{ "terms" }}" class="text-white">Terms of Use</a></li>
                    <li><a href="{{ "#!" }}" class="text-white">Support</a></li>
                    <li><a href="{{ "privacy_policy" }}" class="text-white">Privacy policy</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!--end::Authentication - Sign-up-->
@endsection

@push('js')

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"></script>

<script>
    $(document).ready(function() {
        const $toggleBtn = $('#toggle-password');
        const $passwordField = $('#password');
        const $eyeIcon = $toggleBtn.find('.fa-eye');
        const $eyeSlashIcon = $toggleBtn.find('.fa-eye-slash');

        $toggleBtn.on('click', function() {
            const isPassword = $passwordField.attr('type') === 'password';

            $passwordField.attr('type', isPassword ? 'text' : 'password');
            $eyeIcon.toggleClass('d-none', !isPassword);
            $eyeSlashIcon.toggleClass('d-none', isPassword);

            // Optional: Update ARIA attributes for accessibility
            $toggleBtn.attr('aria-label', isPassword ? 'Hide password' : 'Show password');
        });
    });
</script>

<script>
    $(document).ready(function() {
        $('#toggle-password-otp').on('click', function() {
            var passwordField = $('#otp');
            var passwordFieldType = passwordField.attr('type');

            if (passwordFieldType === 'password') {
                passwordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                passwordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });

    });
</script>

<script>
    $(document).ready(function() {
        var current_fs, next_fs, previous_fs; // Fieldsets
        var opacity;
        var current = 1;
        var steps = $("fieldset").length;

        setProgressBar(current);
        togglePreviousButton(current);

        $(".next").click(function() {
            current_fs = $(this).closest('fieldset');
            next_fs = current_fs.next('fieldset');

            next_fs.show();
            current_fs.animate({
                opacity: 0
            }, {
                step: function(now) {
                    opacity = 1 - now;
                    current_fs.css({
                        'display': 'none',
                        'position': 'relative'
                    });
                    next_fs.css({
                        'opacity': opacity
                    });
                },
                duration: 500
            });
            setProgressBar(++current);
            togglePreviousButton(current);
        });

        $(".previous").click(function() {
            current_fs = $("fieldset:visible");
            previous_fs = current_fs.prev('fieldset');

            previous_fs.show();
            current_fs.animate({
                opacity: 0
            }, {
                step: function(now) {
                    opacity = 1 - now;
                    current_fs.css({
                        'display': 'none',
                        'position': 'relative'
                    });
                    previous_fs.css({
                        'opacity': opacity
                    });
                },
                duration: 500
            });
            setProgressBar(--current);
            togglePreviousButton(current);
        });

        function setProgressBar(curStep) {
            var percent = parseFloat(100 / steps) * curStep;
            percent = percent.toFixed();
            $(".progress-bar").css("width", percent + "%");
        }

        function togglePreviousButton(curStep) {
            if (curStep === 1) {
                $(".previous").hide();
            } else {
                $(".previous").show();
            }
        }

        $(".submit").click(function() {
            return false;
        });
    });


    // phone input country code script
    const input = document.querySelector("#phone");

    const iti = window.intlTelInput(input, {
        separateDialCode: true,
        initialCountry: "us",
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/utils.min.js"
    });




    // file upload
    $(document).ready(function() {
        $('input[type="file"][name="avatar"]').on('change', function(e) {
            var input = this;

            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    $('.image-input-wrapper').css({
                        'background-image': 'url(' + e.target.result + ')',
                        'background-size': 'cover',
                        'background-position': 'center'
                    });

                    $('.image-input').removeClass('image-input-empty');
                }

                reader.readAsDataURL(input.files[0]);
            }
        });
    });
</script>

@endpush