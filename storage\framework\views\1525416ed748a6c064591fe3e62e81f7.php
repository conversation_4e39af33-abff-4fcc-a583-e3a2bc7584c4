<!DOCTYPE html>
<html lang="en">
<!--begin::Head-->

<head>
	<base href="" />
	<title><PERSON><PERSON></title>
	<meta charset="utf-8" />
	<meta name="description" content="The most advanced Bootstrap 5 Admin Theme with 40 unique prebuilt layouts on Themeforest trusted by 100,000 beginners and professionals. Multi-demo, Dark Mode, RTL support and complete React, Angular, Vue, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel versions. Grab your copy now and get life-time updates for free." />
	<meta name="keywords" content="metronic, bootstrap, bootstrap 5, angular, VueJs, React, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel starter kits, admin themes, web design, figma, web development, free templates, free admin themes, bootstrap theme, bootstrap template, bootstrap dashboard, bootstrap dak mode, bootstrap button, bootstrap datepicker, bootstrap timepicker, fullcalendar, datatables, flaticon" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta property="og:locale" content="en_US" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="Metronic - Bootstrap Admin Template, HTML, VueJS, React, Angular. Laravel, Asp.Net Core, Ruby on Rails, Spring Boot, Blazor, Django, Express.js, Node.js, Flask Admin Dashboard Theme & Template" />
	<meta property="og:url" content="https://keenthemes.com/metronic" />
	<meta property="og:site_name" content="Keenthemes | Metronic" />
	<link rel="canonical" href="https://preview.keenthemes.com/metronic8" />
	<link rel="shortcut icon" href="<?php echo e(asset('website')); ?>/assets/media/logos/favicon.ico" />
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
	<link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
	<link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
	<!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="" crossorigin="anonymous"> -->
	<link href="<?php echo e(asset('website/assets/css/style.css')); ?>" rel="stylesheet" type="text/css" />
	<!-- Swipper -->
	<!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" /> -->

	<!--end::Global Stylesheets Bundle-->
	<script>
		// Frame-busting to prevent site from being loaded within a frame without permission (click-jacking) if (window.top != window.self) { window.top.location.replace(window.self.location.href); }
	</script>
	<!-- Swiper CSS -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
	<?php echo $__env->yieldPushContent('css'); ?>
</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu" class="bg-body position-relative app-blank">
	<div class="d-flex flex-column flex-root" id="kt_app_root">

		<?php echo $__env->make('website.templates.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
		<?php echo $__env->yieldContent('content'); ?>
		<?php echo $__env->make('website.templates.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

	</div>
	<div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
		<i class="ki-duotone ki-arrow-up">
			<span class="path1"></span>
			<span class="path2"></span>
		</i>
	</div>
	<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"></script>
	<script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
	<!-- Swiper JS -->
	<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

	<?php echo $__env->yieldPushContent('js'); ?>


	<script>
		$(document).ready(function() {
			// Wishlist Icon Toggle
			$('.wishlist-btn').on('click', function() {
				const icon = $(this).find('i');

				if (icon.hasClass('far')) {
					icon.removeClass('far text-muted').addClass('fas text-danger');
				} else {
					icon.removeClass('fas text-danger').addClass('far text-muted');
				}
			});

			// Swiper: myimageSwiper (with dynamic bullets)
			var myImageSwiper = new Swiper(".myimageSwiper", {
				pagination: {
					el: ".swiper-pagination",
					dynamicBullets: true,
				},
			});

			var mySwiper = new Swiper(".trendingSwiper", {
				slidesPerView: 4,
				spaceBetween: 30,
				pagination: {
					el: ".swiper-pagination",
					clickable: true,
				},
				navigation: {
					nextEl: ".trending-next", // 🔄 Custom button
				},
				breakpoints: {
					768: {
						slidesPerView: 4,
					},
					576: {
						slidesPerView: 1,
					}
				}
			});

			var mySwiper = new Swiper(".professionalSwiper", {
				slidesPerView: 3,
				spaceBetween: 30,
				pagination: {
					el: ".swiper-pagination",
					clickable: true,
				},
				navigation: {
					nextEl: ".professional-next", // 🔄 Custom button
				},
				breakpoints: {
					768: {
						slidesPerView: 3,
					},
					576: {
						slidesPerView: 1,
					}
				}
			});


			var swiper = new Swiper(".testimonialSwiper", {
				slidesPerView: 1,
				spaceBetween: 30,
				pagination: {
					el: ".swiper-pagination",
					clickable: true,
				},

			});
		});
	</script>

</body>
<!--end::Body-->

</html><?php /**PATH D:\Github\vadu\resources\views/website/layout/master.blade.php ENDPATH**/ ?>