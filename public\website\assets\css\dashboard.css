:root {
    --dark-black: #000;
    --white: #fff;
    --jet-black :#1A1A1A;
    --burnt-peach :#EC9B6E;
    --dusk-blue : #656B76;
    --midnight-navy :#032642;
    --midnight-slate :#242B3A;
    --pale-silver : #F6F7F7;
    --reddish-orange : #f15a29;
    --cloud-gray :#EBECED;
    --green-text: #12B76A;
    --orange-text: #C4320A;

}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}


/*Heading Font Sizes */
h1 {font-size: 30px; font-family: "Inter", sans-serif;}
h2 {font-size: 28px; font-family: "Archivo", sans-serif;}
h3 {font-size: 24px; font-family: "Archivo", sans-serif;}
h4 {font-size: 32px; font-family: "Inter", sans-serif;}
h5 {font-size: 18px; font-family: "Inter", sans-serif;}
h6 {font-size: 14px; font-family: "Inter", sans-serif;}
p {font-size: 16px; font-family: "Inter", sans-serif;color: #535862; font-weight: 400;}

h1,h2,h3,h4,h5,h6 {line-height: 1.2;word-break: break-word;margin: 0px;}
p,a,span {line-height: 1.6;word-break: break-word;text-decoration: none;}
ul,ol {margin: 0px;padding: 0px;}
li {font-size: 16px; font-family: "Inter", sans-serif;}
p {margin: 0px;}

/* Custom-Font-size */
.fs-48 {font-size: 48px; line-height: 1.2; font-family: "Inter", sans-serif;}
.fs-19 {font-size: 19px; line-height: 1.2; font-family: "Inter", sans-serif;}
.fs-16 {font-size: 16px; line-height: 1.2; font-family: "Inter", sans-serif;}
.fs-15 {font-size: 15px; line-height: 1.2; font-family: "Inter", sans-serif;}
.fs-14 {font-size: 14px; line-height: 1.2; font-family: "Inter", sans-serif;}



/* custom-color-classes */
.text-dark-black {color: var(--dark-black);}
.text-white {color: var(--white);}
.text-jet-black {color: var(--jet-black);}
.text-burnt-peach {color: var(--burnt-peach);}
.text-dusk-blue {color: var(--dusk-blue);}
.text-midnight-navy {color: var(--midnight-navy);}
.text-midnight-slate {color: var(--midnight-slate);}
.pale-silver {background: var(--pale-silver);}
.reddish-orange{color: var(--reddish-orange);}
.dusk-blue{color: var(--dusk-blue);opacity: 0.6;}
.text-gridient {background: linear-gradient(90deg, #EC9B6E 58.81%, #9D6FD9 80.66%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
/* bg-Linear-gridient */
.bg-linear {background: linear-gradient(0deg, rgb(255 255 255 / 96%) 0%, rgb(255 255 255 / 91%) 100%), linear-gradient(90deg, #EC9B6E 48.36%, #9D6FD9 100%);}
.green-text{color: var(--green-text);}
.orange-text{ color: var(--orange-text);}


/* font-weight  */
.fw-700 {font-weight: 700;}
.fw-600{font-weight: 600;}
.fw-500{font-weight: 500;}
.fw-400{font-weight: 400;}
.fw-300{font-weight: 300;}

/* Border-color Radius */
.border-dusk-blue {border: 1px solid var(--dusk-blue);}
.border-radius {border-radius: 50px;}

/* box-shadow */
.newsLettershadow {border-radius: 10px;background: #FBFBFB;box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.08);padding: 14.99px 15px 15px 15px; }

/* Global-Buttons */
.btn-gridient:hover {background: linear-gradient(120deg, #9d6fd9 40.36%, #ec9b6e 100%);color:var(--white);}
.btn-gridient {font-family: Archivo; border-radius: 50px;background: linear-gradient(90deg, #ec9b6e 48.36%, #9d6fd9 100%);color: white;padding: 10px 20px;transition: 0.3s ease;}
.btn_white {background: #fff;border-radius: 8px;height: 40px;padding: 0px 15px;border: 1px solid #D5D7DA;font-size: 14px;font-weight: 600;}
.btn_purple {background: #9B6CD6;color: #fff;border-radius: 8px;border: 1px solid #9B6CD6;height: 40px;align-content: center;padding: 0px 15px;}

/* Footer-Input */
input.form-control:focus {box-shadow: none;border: 1px solid #DCDCDC;}
input.form-control, .form-select {height: 44px;padding: 13px 15px 13px 15px;border-radius: 8px;border: 1px solid #DCDCDC;background: var(--white);font-size: 16px;font-weight: 500;font-family: 'Inter';color: #181D27;}

/* date-range-picker */
.date_range_box {border: 1px solid #D5D7DA;border-radius: 8px;background: #fff;font-size: 14px;height: 40px;align-content: center;padding: 0px 15px;outline: none;font-family: 'Inter';font-weight: 600;}
.date_range_picker {font-size: 14px;outline: none;font-family: 'Inter';font-weight: 600;border: none;}
.dropify-wrapper {border-radius: 8px;}
/* input css */
.form-label {font-family: 'Inter';font-size: 14px;font-weight: 500;color: #414651;}
.select2-container--bootstrap-5 .select2-selection {border-radius: 8px !important;min-height: 44px !important;border: 1px solid #ced4da !important;align-content: center;}

/* sidebar css */
#kt_app_sidebar .menu-link .menu-title {color: #414651;font-size: 14px;font-weight: 600;max-width: max-content;}
#kt_app_sidebar .menu-link:hover .menu-title, #kt_app_sidebar .menu-link.active .menu-title{background: linear-gradient(90deg, #EC9B6E 58.81%, #9D6FD9 80.66%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
#kt_app_sidebar .menu-link:hover .menu-icon img, #kt_app_sidebar .menu-link.active .menu-icon img{filter: brightness(0) saturate(100%) invert(86%) sepia(12%) saturate(3042%) hue-rotate(311deg) brightness(95%) contrast(94%);}
#kt_app_sidebar .menu-link{height: 40px;position: relative;border-radius: 6px;}
#kt_app_sidebar .menu-link.active, #kt_app_sidebar .menu-link:hover{background: linear-gradient(0deg, rgba(255, 255, 255, 0.80) 0%, rgba(255, 255, 255, 0.80) 100%), linear-gradient(90deg, #EC9B6E 48.36%, #9D6FD9 100%);}
#kt_app_sidebar .menu-item.menu-accordion > .menu-link::after {content: "\f107";position: absolute;right: 15px;top: 9px;color: #414651;font-family: "Font Awesome 6 Free";font-weight: 900;transition: .3s ease-in-out;}
#kt_app_sidebar .menu-item.menu-accordion.show > .menu-link::after {content: "\f106";position: absolute;right: 15px;top: 9px;color: #414651;font-family: "Font Awesome 6 Free";font-weight: 900;transition: .3s ease-in-out;}
#kt_app_sidebar .profile_info {padding: .65rem 1rem;}
#kt_app_sidebar .profile_info .text {display: inline-block;max-width: 125px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;vertical-align: middle;}


.gray-cards{ border: 1px solid var(--Gray-200, #E9EAEB); background: var(--White, #FFF); box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05);  padding: 20px 24px;}



 .table thead th {   font-weight: 600;    font-size: 0.8rem;   color: #8e8ea9;   border-bottom: 1px solid #ebeced;}
 .table tbody tr {   border-bottom: 1px solid #ebeced;}
 .avatar { width: 32px; height: 32px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: 0.75rem; user-select: none; margin-right: 0.75rem; flex-shrink: 0;}
 .avatar.orange {  background-color: #f15a29;}
 .avatar.brown { background-color: #6b5239;}
 .avatar.purple {  background-color: #a39dea;  color: #5e56c6;}
 .star {  color: #f15a29;  font-size: 0.8rem;  margin-right: 0.3re}
 .status-badge { font-size: 0.75rem; font-weight: 600; padding: 0.2rem 0.7rem; border-radius: 50px; color: white; display: inline-block; min-width: 60px; text-align: center;}
 .status-active { background-color: #a1d89f;color: #1e7e34;}
 .status-inactive { background-color: #f1a1a1;   color: #d42f2f;}
 .search-input { max-width: 240px; font-size: 0.8rem; padding: 0.25rem 0.75rem; border-radius: 6px; border: 1px solid #ddd; background-color: #fefefe;}
 .pagination .page-link { font-size: 0.8rem; border-radius: 6px; border: none; color: #a39dea }
 .pagination .page-link:hover {background-color: #e0d9ff; color: #5e56c6;}
 .pagination .page-item.active .page-link {  color: white;   border-radius: 6px;}
 .data-table .active>.page-link,.page-link.active { background-color: #e3e3e3;}
 .table-actions {  font-size: 1.1rem;   color: #b8b7cc;  cursor: pointer;  user-select: none;}
 .data-table .pagination .page-link {  color: #7c7c7c;}
.data-table .form-control:focus { box-shadow: none;}
 button.page-link.btn-sm.previous { border: 1px solid;}
 button.page-link.btn-sm.next {    border: 1px solid;}
 .data-table .page-link:focus {   box-shadow: none;}
 .data-table .form-control.search-input {  border: 1px solid;  border-radius: 6px;  padding: 7px;  border-color: transparent;}
.search-icon{ position: relative;}
 .data-table .search-input{  border: 1px solid transparent;  padding: 0px;   max-width: 259px;}
 .data-table .pagination .page-link:hover {  background-color: white;}
 .data-table .form-control:focus {border-color: #e4e2e2;}


  .status-cards p.green-text { background: #ECFDF3;}
  .status-cards p.orange-text { background: #FFF6ED;}
 .status-cards p.green-text, .status-cards p.orange-text { padding: 2px 5px; border-radius: 16px; }
 .green-text i{color: var(--green-text);}
 .orange-text i{color: var(--orange-text);}



 /* table ui */
        .table_container {background: white;border-radius: 12px;padding: 24px;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);margin-bottom: 20px;}

        .search_container {margin-bottom: 20px;position: relative;display: inline-block;}
        #customSearch { width: 300px; font-family: 'Inter'; padding: 8px 16px 8px 40px; border: 1px solid #D5D7DA; border-radius: 8px; font-size: 16px; font-weight: 400; background-color: #fff; height: 40px; box-shadow: 0px 1px 2px 0px #0A0D120D; color: #717680; }
        #customSearch:focus {outline: none;border-color: #D5D7DA;background-color: white;}
        body .table_header .search_icon {position: absolute;left: 12px;top: 50%;transform: translateY(-50%);color: #717680;font-size: 16px;pointer-events: none;}

        .profile_card {display: flex;align-items: center;gap: 12px;}
        .profile_info {display: flex;flex-direction: column;}
        .name {font-weight: 600;color: #1f2937;font-size: 14px;margin: 0;}
        .rating {display: flex;align-items: center;gap: 4px;margin-top: 2px;}
        body table td .star {color: #F15A29;font-size: 12px;}
        .rating_text {color: #6b7280;font-size: 12px;}
        .action_btn button {border: 1px solid #D5D7DA;width: 36px;height: 36px;border-radius: 8px;background: #fff;display: flex;align-items: center;justify-content: center;}

        .plan_badge {padding: 4px 8px;border-radius: 6px;font-size: 12px;font-weight: 500;}
        .plan_free {background-color: #f3f4f6;color: #374151;}
        .plan_pro {background-color: #dbeafe;color: #1d4ed8;}
        .plan_exclusive {background-color: #fef3c7;color: #d97706;}

        .status_badge {padding: 4px 8px;border-radius: 6px;font-size: 12px;font-weight: 500;display: inline-flex;align-items: center;gap: 4px;}
        .status_active {background-color: #dcfce7;color: #166534;}
        .status_inactive {background-color: #fee2e2;color: #dc2626;}
        .status_dot {width: 6px;height: 6px;border-radius: 50%;}
        .status_active .status_dot {background-color: #16a34a;}
        .status_inactive .status_dot {background-color: #dc2626;}

        .email_text {color: #6b7280;font-size: 14px;}
        .service_text {color: #374151;font-size: 14px;}
        .date_text {color: #6b7280;font-size: 14px;}

        #myTable thead th {border-top: 1px solid #e5e7eb;border-bottom: 1px solid #e5e7eb;color: #6b7280;font-weight: 500;font-size: 12px;text-transform: uppercase;letter-spacing: 0.5px;padding: 12px 16px;}
        #myTable tbody td {padding: 16px;border-bottom: 1px solid #f3f4f6;vertical-align: middle;color: #6b7280;font-size: 14px;}
        #myTable tbody tr:hover {background-color: #f9fafb;}
        #myTable tbody td .rounded-square {width: 60px;height: 40px;object-fit: cover;border-radius: 6px;}
        table tbody td .dropdown > button {border: none;background: none;}

        .dataTables_paginate {margin-top: 20px;display: flex;justify-content: space-between;align-items: center;}
        .dataTables_paginate .paginate_button {padding: 8px 12px;margin: 0 2px;border: 1px solid #d1d5db;background: white;color: #374151;border-radius: 6px;text-decoration: none;font-size: 14px;min-width: 40px;text-align: center;}
        .dataTables_paginate .paginate_button:hover {background: #f3f4f6;border-color: #d1d5db;color: #374151;}
        .dataTables_paginate .paginate_button.current {background: #3b82f6;border-color: #3b82f6;color: white;}
        .dataTables_paginate .paginate_button.disabled {opacity: 0.5;cursor: not-allowed;background: #f9fafb;color: #9ca3af;}
        .dataTables_paginate .paginate_button.disabled:hover {background: #f9fafb;border-color: #d1d5db;}
        .pagination_info {color: #6b7280;font-size: 14px;}
        .table_header{display: flex; align-items: center; justify-content: space-between;margin-bottom: 30px;}
        .dataTables_wrapper .dataTables_paginate .paginate_button {border-radius: 8px;border: 1px solid #D5D7DA;font-size: 14px;font-weight: 600;}
.dataTables_wrapper .dataTables_paginate span .paginate_button {border: 1px solid #D5D7DA;}
#myTable .dataTables_wrapper .dataTables_paginate span .paginate_button.current {background: #F9F5FF;color: #9B6CD6;border: none;}
div.dataTables_wrapper div.dataTables_paginate {display: flex;align-items: center;justify-content: space-between;width: 100%;}
.dataTables_wrapper .dataTables_paginate .paginate_button.current {background: #F9F5FF;color: #9B6CD6 !important;}
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {background: #F9F5FF;color: #9B6CD6 !important;border: 1px solid #D5D7DA;}
.nav.page_tabs {width: max-content;border: 1px solid #D5D7DA;border-radius: 8px;overflow: hidden;}
.nav.page_tabs li a {border-radius: 0px;border: none;border-right: 1px solid #D5D7DA;background: #fff;color: #414651;font-size: 14px;}
.nav.page_tabs li a.active {color: #181D27;font-weight: 600;}




.card.subscription {border-radius: 12px;border: 1px solid #E9EAEB;}
.card.subscription ul {list-style: none;}
.card.subscription h6 {font-family: 'Inter';font-size: 17px;font-weight: 600;color: #181D27;margin-bottom: 10px;}
.card.subscription h2 {font-family: 'Inter';font-size: 48px;font-weight: 600;color: #181D27;margin-bottom: 10px;}
.card.subscription h2 span { font-size: 20px; font-weight: 400; font-family: 'Inter'; }
.card.subscription p {font-family: 'Inter';font-size: 13px;font-weight: 400;color: #535862;margin-bottom: 20px;}
.card.subscription ul li i {color: #181D27;font-size: 20px;margin-right: 10px;}
.card.subscription ul li {margin-bottom: 10px;font-family: 'Inter';font-size: 13px;font-weight: 400;color: #535862;}

 /* modal css */
.modal-backdrop.show {opacity: .1;}
.modal-content {background: #fff;border-radius: 6px;border: none;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);}

/* subscription feature box */
.feature-box {display: flex;align-items: center;justify-content: space-between;border: 1px solid #ccc;border-radius: 8px;margin-bottom: 10px;overflow: hidden;}
    .feature-box input {border: none;background: transparent;flex-grow: 1;margin-left: 10px;font-weight: 500;}
    .feature-box input:focus {outline: none;}
    .no-features {display: none;color: #888;font-style: italic;margin-bottom: 10px;}
    .add-more-btn {border: 1px solid #ddd;padding: 10px 15px;border-radius: 8px;background-color: #f9f9f9;cursor: pointer;display: inline-flex;align-items: center;color: #7e57c2;font-weight: 500;}
    .add-more-btn i{color: #7e57c2;}
    .add-more-btn i {margin-right: 5px;}
    .delete-btn {color: #dc3545;cursor: pointer;}
    .feature-box i {padding: 16px;background: #D5D7DA;border-right: 1px solid #D5D7DA;color: #281D1B;}
    .feature-box .delete-btn {padding: 16px;background: #fff;border: none;border-left: 1px solid #D5D7DA;color: #FF4242;}




.settings .nav-link{background: none;border: none;font-family: 'Inter';font-size: 14px;font-weight: 600;color: #717680;}
.settings .nav-link.active{background: none;color: #9B6CD6;border-bottom: 2px solid #9B6CD6;}
.input-group .input-group-text {border-top-left-radius: 8px;border-bottom-left-radius: 8px;width: 40px;display: flex;align-items: center;justify-content: center;}
.input-group .input-group-text i {font-size: 16px;color: #717680;}
.toggle-password {position: absolute;right: 25px;top: 50%;transform: translateY(-50%);cursor: pointer;color: #717680;}
.country-select-wrapper {position: relative;}
  .country-select {width: 100%;padding: 10px 10px 10px 45px;appearance: none;-webkit-appearance: none;}
  .flag-icon {position: absolute;top: 50%;left: 10px;width: 25px;height: 25px;border-radius: 50%;overflow: hidden;transform: translateY(-50%);}
  .flag-icon img {width: 20px;height: 20px;object-fit: cover;border-radius: 50%;}

/* header hidden */
#kt_app_header {display: none;}