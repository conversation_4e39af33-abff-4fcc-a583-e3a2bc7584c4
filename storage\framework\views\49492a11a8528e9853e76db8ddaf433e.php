<!--begin::Sidebar-->
<div id="kt_app_sidebar" class="app-sidebar flex-column bg-white border-end" data-kt-drawer="true" data-kt-drawer-name="app-sidebar"
    data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px"
    data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="app-sidebar-logo px-6 border-0" id="kt_app_sidebar_logo">
        <a href="<?php echo e(route('home')); ?>">
            <img src="<?php echo e(asset('website/assets/media/images/vadu-website-logo.svg')); ?>" alt="Logo" width="127" height="40">
            <!-- <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->logo ??''); ?>"
                class="app-sidebar-logo-default" />
            <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/media/logos/default-small.svg"
                class="h-20px app-sidebar-logo-minimize" /> -->
        </a>
    </div>

    <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper">
            <div id="kt_app_sidebar_menu_scroll" class=" my-5 mx-3" data-kt-scroll="true"
                data-kt-scroll-activate="true" data-kt-scroll-height="auto"
                data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px"
                data-kt-scroll-save-state="true">
                <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6" id="#kt_app_sidebar_menu"
                    data-kt-menu="true" data-kt-menu-expand="false">

                    <!-- dashboard menu items list start -->
                    <?php if(auth()->user()->hasRole('developer')): ?>
                    <div class="menu-item pt-5">
                        <div class="menu-content">
                            <span class="menu-heading fw-bold text-uppercase fs-7">Developer</span>
                        </div>
                    </div>

                    <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                        <span class="menu-link">
                            <span class="menu-icon">
                                <i class="ki-duotone ki-abstract-28 fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </span>
                            <span class="menu-title">User Management</span>
                            <span class="menu-arrow"></span>
                        </span>
                        <div class="menu-sub menu-sub-accordion">
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('crud-list')): ?>
                                <span class="menu-link">
                                    <span class="menu-bullet">
                                        <span class="bullet bullet-dot"></span>
                                    </span>
                                    <span class="menu-title">CRUD</span>
                                    <span class="menu-arrow"></span>
                                </span>
                                <div class="menu-sub menu-sub-accordion">
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo e(url('crud_generator')); ?>">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">CRUD Generator</span>
                                        </a>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user-list')): ?>
                                    <span class="menu-link">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Users</span>
                                        <span class="menu-arrow"></span>
                                    </span>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('users')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Users List</span>
                                            </a>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role-list')): ?>
                                    <span class="menu-link">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Roles</span>
                                        <span class="menu-arrow"></span>
                                    </span>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('roles')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Roles List</span>
                                            </a>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="menu-item">
                                    <a class="menu-link" href="javascript:void(0);">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Permissions</span>
                                    </a>
                                </div>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings-list')): ?>
                                <div class="menu-item">
                                    <a class="menu-link" href="<?php echo e(url('settings')); ?>">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Settings</span>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <hr>

                    <?php endif; ?>

                    <!-- admin menu items list start -->
                    <?php if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('professional')): ?>
                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('home') ? 'active' : ''); ?>" href="<?php echo e(url('home')); ?>">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/icon-dashboard.svg">
                            </span>
                            <span class="menu-title">Dashboard</span>
                        </a>
                    </div>
                    <?php if(auth()->user()->hasRole('admin')): ?>
                    <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                        <span class="menu-link">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/icon-professionals.svg">
                            </span>
                            <span class="menu-title">Professionals</span>
                        </span>
                        <div class="menu-sub menu-sub-accordion">
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('admin/contractors') ? 'active' : ''); ?>" href="<?php echo e(route('admin.contractors')); ?>">
                                    <span class="menu-bullet"></span>
                                    <span class="menu-title">Contractors</span>
                                </a>
                            </div>

                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('admin/title-escrow') ? 'active' : ''); ?>" href="<?php echo e(route('admin.title-escrow')); ?>">
                                    <span class="menu-bullet">
                                    </span>
                                    <span class="menu-title">Title & Escrow</span>
                                </a>
                            </div>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('admin/mortgage-lenders') ? 'active' : ''); ?>" href="<?php echo e(route('admin.mortgage-lenders')); ?>">
                                    <span class="menu-bullet">
                                    </span>
                                    <span class="menu-title">Mortgage Lenders</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('admin/service-categories') ? 'active' : ''); ?>" href="<?php echo e(route('admin.service-categories')); ?>">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/icon-services.svg">
                            </span>
                            <span class="menu-title">Service Categories</span>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if(auth()->user()->hasRole('professional')): ?>
                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('professional/projects') ? 'active' : ''); ?>" href="<?php echo e(route('professional.projects')); ?>">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/media/images/projects-icon.png">
                            </span>
                            <span class="menu-title">My Projects</span>
                        </a>
                    </div>

                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('professional/quote-request') ? 'active' : ''); ?>" href="<?php echo e(route('professional.quote-request')); ?>">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/media/images/projects-icon.png">
                            </span>
                            <span class="menu-title">Quote Request</span>
                        </a>
                    </div>
                    <?php endif; ?>
                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('admin/subscription') ? 'active' : ''); ?>" href="<?php echo e(route('admin.subscription')); ?>">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/icon-subscription.svg">
                            </span>
                            <span class="menu-title">Subscription</span>
                        </a>
                    </div>

                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('admin/chats') ? 'active' : ''); ?>" href="javascript:void(0);">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/icon-chats.svg">
                            </span>
                            <span class="menu-title">Chats</span>
                        </a>
                    </div>
                      <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('admin/chats') ? 'active' : ''); ?>" href="javascript:void(0);">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/media/images/services-icon.svg" width="24" height="24" alt="Services Icon"}">
                            </span>
                            <span class="menu-title">Services</span>
                        </a>
                    </div>

                    <?php if(auth()->user()->hasRole('admin')): ?>
                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is('admin/cms') ? 'active' : ''); ?>" href="javascript:void(0);">
                            <span class="menu-icon">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/icon-cms.svg">
                            </span>
                            <span class="menu-title">CMS</span>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php endif; ?>

                    <?php $__currentLoopData = $crud; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($item->url . '-list')): ?>
                    <div class="menu-item">
                        <a class="menu-link <?php echo e(request()->is($item->url) ? 'active' : ''); ?>"
                            href="<?php echo e(url($item->url ?? 'home')); ?>">
                            <span class="menu-icon">
                                <i class="ki-duotone ki-abstract-28 fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </span>
                            <span class="menu-title"><?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->name)); ?></span>
                        </a>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>

    <div class="app-sidebar-footer flex-column-auto pt-2 pb-6 px-3" id="kt_app_sidebar_footer">
        <?php if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('professional')): ?>
        <div class="menu-item">
            <a class="menu-link <?php echo e(request()->is('notifications') ? 'active' : ''); ?>" href="<?php echo e(url('notifications')); ?>">
                <span class="menu-icon">
                    <img src="<?php echo e(asset('website')); ?>/assets/images/icon-notification.svg">
                </span>
                <span class="menu-title">Notification</span>
            </a>
        </div>
        <div class="menu-item">
            <a class="menu-link <?php echo e(request()->is('profile_settings') ? 'active' : ''); ?>" href="<?php echo e(url('profile_settings')); ?>">
                <span class="menu-icon">
                    <img src="<?php echo e(asset('website')); ?>/assets/images/icon-settings.svg">
                </span>
                <span class="menu-title">Settings</span>
            </a>
        </div>

        <hr>

        <div class="profile_info">
            <div class="d-flex align-items-center">
                <div class="symbol symbol-50px me-3">
                    <img src="<?php echo e(asset('website')); ?>/assets/media/avatars/300-3.jpg" class="rounded-circle" alt="">
                </div>
                <div class="d-flex justify-content-start flex-column me-3">
                    <a href="javascript:void(0);" class="text-gray-800 fw-bold text-hover-primary mb-1 fs-6 text">Olivia Rhye</a>
                    <span class="text-gray-400 fw-semibold d-block fs-7 text"><EMAIL></span>
                </div>
                <a class="" href="<?php echo e(url('logout')); ?>">
                    <span class="menu-icon">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/icon-logout.svg">
                    </span>
                </a>
            </div>
        </div>
        <?php endif; ?>
        <?php if(auth()->user()->hasRole('developer')): ?>

        <a href="<?php echo e(url('html/demo1/dist')); ?>"
            class="btn btn-flex flex-center btn-custom btn-primary overflow-hidden text-nowrap px-0 h-40px w-100"
            data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss-="click"
            title="200+ in-house components and 3rd-party plugins">
            <span class="btn-label">Docs & Components</span>
            <i class="ki-duotone ki-document btn-icon fs-2 m-0">
                <span class="path1"></span>
                <span class="path2"></span>
            </i>
        </a>
        <?php endif; ?>
    </div>
</div>
<!--end::Sidebar--><?php /**PATH D:\Github\vadu\resources\views/theme/layout/sidebar.blade.php ENDPATH**/ ?>