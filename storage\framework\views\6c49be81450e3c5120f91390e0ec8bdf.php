<?php $__env->startSection('content'); ?>

<section>
    <div class="container">
        <div class="row">
            <div class="col-12 mb-10">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="page_title">
                        <h1>Subscription</h1>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="card subscription">
                    <div class="card-body">
                        <h6>Gold</h5>
                            <h2>$0</h2>
                            <p>For short-term users</p>
                            <?php if(auth()->user()->hasRole('admin')): ?>
                            <button type="button" class="btn_white w-100" data-bs-toggle="modal" data-bs-target="#exampleModal">Current Plan</button>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasRole('professional')): ?>
                            <button type="button" class="btn_white w-100">Edit Details</button>
                            <?php endif; ?>
                            <hr>
                            <ul>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Unlimited Listings
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Basic Support
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Access to all features
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> No long-term commitment
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Cancel anytime
                                </li>
                            </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card subscription">
                    <div class="card-body">
                        <h6>Gold</h5>
                            <h2>$49 <span>Monthly</span> </h2>
                            <p>For short-term users</p>
                            <?php if(auth()->user()->hasRole('admin')): ?>
                            <button type="button" class="btn_purple w-100" data-bs-toggle="modal" data-bs-target="#exampleModal">Upgrade Plan</button>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasRole('professional')): ?>
                            <button type="button" class="btn_white w-100">Edit Details</button>
                            <?php endif; ?>
                            <hr>
                            <ul>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Unlimited Listings
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Basic Support
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Access to all features
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> No long-term commitment
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Cancel anytime
                                </li>
                            </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card subscription">
                    <div class="card-body">
                        <h6>Gold</h5>
                            <h2>$99 <span>Monthly</span> </h2>
                            <p>For short-term users</p>
                            <?php if(auth()->user()->hasRole('admin')): ?>
                            <button type="button" class="btn_purple w-100" data-bs-toggle="modal" data-bs-target="#exampleModal">Upgrade Plan</button>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasRole('professional')): ?>
                            <button type="button" class="btn_white w-100">Edit Details</button>
                            <?php endif; ?>
                            <hr>
                            <ul>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Unlimited Listings
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Basic Support
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Access to all features
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> No long-term commitment
                                </li>
                                <li>
                                    <span><i class="fa-solid fa-check"></i></span> Cancel anytime
                                </li>
                            </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<!-- Button trigger modal -->
<!-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">
  Launch demo modal
</button> -->

<!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel">Edit Details</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="#">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="plan_name" class="form-label">Plan Name</label>
                        <input type="text" class="form-control" name="plan_name" id="plan_name" placeholder="Enter Plan Name">
                    </div>
                    <div class="mb-3">
                        <label for="plan_price" class="form-label">Another label</label>
                        <input type="text" class="form-control" name="plan_price" id="plan_price" placeholder="Enter Plan Price">
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label">Plan Features</label>
                        <div id="featureContainer"></div>
                        <div class="no-features" id="noFeaturesMsg">
                            No features added yet. Click "Add More" to get started!
                        </div>
                        <div class="add-more-btn mt-2" id="addFeatureBtn">
                            <i class="fas fa-plus"></i> Add More
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn_white" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn_purple">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    function updateEmptyMessage() {
        if ($('#featureContainer .feature-box').length === 0) {
            $('#noFeaturesMsg').show();
        } else {
            $('#noFeaturesMsg').hide();
        }
    }

    function createFeature(value = 'Unlimited Listings') {
        return $(`
        <div class="feature-box">
          <div class="d-flex align-items-center w-100">
            <i class="fas fa-check me-2"></i>
            <input type="text" class="form-control p-0 border-0 bg-transparent" value="${value}">
          </div>
          <i class="fas fa-trash delete-btn ms-3"></i>
        </div>
      `);
    }

    $(document).ready(function() {
        $('#addFeatureBtn').click(function() {
            const feature = createFeature();
            $('#featureContainer').append(feature);
            updateEmptyMessage();
        });

        $('#featureContainer').on('click', '.delete-btn', function() {
            $(this).closest('.feature-box').remove();
            updateEmptyMessage();
        });

        updateEmptyMessage();
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Github\vadu\resources\views/dashboard/admin/subscription.blade.php ENDPATH**/ ?>